import json
import logging
import os
from abc import ABC, abstractmethod
from Tool.config_manager import ConfigManager

class OCREngine(ABC):
    """OCR引擎抽象基类"""
    
    @abstractmethod
    def is_initialized(self):
        """检查引擎是否已初始化"""
        pass
    
    @abstractmethod
    def recognize_text(self, image_path):
        """识别图片中的文字"""
        pass
    
    @abstractmethod
    def recognize_text_from_bytes(self, image_bytes):
        """从图片字节数据识别文字"""
        pass
    
    def image_to_text(self, image_path):
        """兼容性方法"""
        return self.recognize_text(image_path)

class BaiduOCRLocal(OCREngine):
    """本地百度OCR引擎"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.client = None
        self._init_local_engine()
    
    def _init_local_engine(self):
        """初始化本地百度OCR引擎"""
        try:
            from aip import AipOcr
            
            config_manager = ConfigManager()
            ocr_config = config_manager.get_ocr_config()
            baidu_config = ocr_config.get('baidu_ocr', {})
            
            app_id = baidu_config.get('app_id')
            api_key = baidu_config.get('api_key')
            secret_key = baidu_config.get('secret_key')
            
            if not all([app_id, api_key, secret_key]):
                self.logger.error("百度OCR配置不完整，缺少app_id、api_key或secret_key")
                return
            
            self.client = AipOcr(app_id, api_key, secret_key)
            self.logger.info("本地百度OCR引擎初始化成功")
            
        except ImportError:
            self.logger.error("缺少百度OCR SDK，请安装: pip install baidu-aip")
        except Exception as e:
            self.logger.error(f"初始化本地百度OCR引擎失败: {str(e)}")
    
    def is_initialized(self):
        """检查引擎是否已初始化"""
        return self.client is not None
    
    def recognize_text(self, image_path):
        """识别图片中的文字"""
        if not self.is_initialized():
            self.logger.error("本地百度OCR引擎未初始化")
            return ""
        
        if not os.path.exists(image_path):
            self.logger.error(f"图片文件不存在: {image_path}")
            return ""
        
        try:
            with open(image_path, 'rb') as fp:
                image_bytes = fp.read()
                return self.recognize_text_from_bytes(image_bytes)
        except Exception as e:
            self.logger.error(f"读取图片文件失败: {str(e)}")
            return ""
    
    def recognize_text_from_bytes(self, image_bytes):
        """从图片字节数据识别文字"""
        if not self.is_initialized():
            self.logger.error("本地百度OCR引擎未初始化")
            return ""
        
        try:
            # 使用百度OCR高精度识别
            result = self.client.accurateBasic(image_bytes)
            
            if 'words_result' in result:
                texts = [item['words'] for item in result['words_result']]
                recognized_text = "\n".join(texts)
                self.logger.info(f"OCR识别成功，识别到 {len(texts)} 行文字")
                return recognized_text
            else:
                error_msg = result.get('error_msg', '未知错误')
                self.logger.warning(f"百度OCR识别失败: {error_msg}")
                return ""
                
        except Exception as e:
            self.logger.error(f"百度OCR识别失败: {str(e)}")
            return ""

class OCRFactory:
    """OCR 工厂类，用于创建和管理 OCR 引擎"""
    _ocr_engine = None
    _initialized = False
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        if not OCRFactory._initialized:
            self._initialize_engine()
            OCRFactory._initialized = True
    
    def _initialize_engine(self):
        """初始化OCR引擎"""
        # 读取配置，确定使用本地还是远程OCR
        try:
            config_manager = ConfigManager()
            ocr_config = config_manager.get_ocr_config()
            ocr_mode = ocr_config.get('ocr_mode', 'local')  # 默认使用本地模式
            
            self.logger.info(f"OCR模式: {ocr_mode}")
            
            if ocr_mode == 'remote':
                self._init_remote_engine()
            else:
                self._init_baidu_engine()
                
        except Exception as e:
            self.logger.error(f"初始化OCR引擎时出错: {str(e)}")
            OCRFactory._ocr_engine = None
    
    def _init_remote_engine(self):
        """初始化远程百度OCR引擎"""
        self.logger.info("正在初始化远程百度OCR引擎...")
        
        try:
            from Tool.baidu_ocr_client import RemoteBaiduOCR
            remote_ocr = RemoteBaiduOCR()
            if remote_ocr.is_initialized():
                OCRFactory._ocr_engine = remote_ocr
                self.logger.info("远程百度OCR引擎初始化成功")
            else:
                self.logger.warning("远程百度OCR引擎初始化失败，将尝试使用本地引擎")
                self._init_baidu_engine()
        except Exception as e:
            self.logger.error(f"初始化远程百度OCR引擎时出错: {str(e)}")
            self._init_baidu_engine()
    
    def _init_baidu_engine(self):
        """初始化本地百度OCR引擎"""
        self.logger.info("正在初始化本地百度OCR引擎...")
        
        try:
            baidu_ocr = BaiduOCRLocal()
            if baidu_ocr.is_initialized():
                OCRFactory._ocr_engine = baidu_ocr
                self.logger.info("本地百度OCR引擎初始化成功")
            else:
                self.logger.warning("本地百度OCR引擎初始化失败")
                OCRFactory._ocr_engine = None
        except Exception as e:
            self.logger.error(f"初始化本地百度OCR引擎时出错: {str(e)}")
            OCRFactory._ocr_engine = None
    
    def get_ocr_engine(self, engine_type=None):
        """获取OCR引擎实例"""
        if OCRFactory._ocr_engine is None:
            self.logger.warning("OCR引擎未初始化或初始化失败")
        return OCRFactory._ocr_engine
    
    def switch_engine(self, engine_type):
        """切换OCR引擎类型"""
        try:
            config_manager = ConfigManager()
            ocr_config = config_manager.get_ocr_config()
            ocr_config['ocr_mode'] = engine_type
            
            if config_manager.save_ocr_config(ocr_config):
                # 重新初始化引擎
                OCRFactory._initialized = False
                OCRFactory._ocr_engine = None
                self._initialize_engine()
                OCRFactory._initialized = True
                self.logger.info(f"已切换到 {engine_type} OCR引擎")
                return True
            else:
                self.logger.error("保存OCR配置失败")
                return False
        except Exception as e:
            self.logger.error(f"切换OCR引擎失败: {str(e)}")
            return False
    
    def test_engine(self):
        """测试当前OCR引擎"""
        if OCRFactory._ocr_engine is None:
            return False, "OCR引擎未初始化"
        
        try:
            if hasattr(OCRFactory._ocr_engine, 'test_connection'):
                # 远程OCR引擎
                return OCRFactory._ocr_engine.test_connection()
            else:
                # 本地OCR引擎
                if OCRFactory._ocr_engine.is_initialized():
                    return True, "本地百度OCR引擎可用"
                else:
                    return False, "本地百度OCR引擎未正确初始化"
        except Exception as e:
            return False, f"测试OCR引擎失败: {str(e)}"

# 全局OCR工厂实例
_ocr_factory = None

def get_ocr_factory():
    """获取OCR工厂实例"""
    global _ocr_factory
    if _ocr_factory is None:
        _ocr_factory = OCRFactory()
    return _ocr_factory

def get_ocr_engine():
    """获取OCR引擎实例"""
    factory = get_ocr_factory()
    return factory.get_ocr_engine()
