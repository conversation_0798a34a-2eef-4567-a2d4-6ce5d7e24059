import base64
import json
import os
import subprocess
import socket
import sys
import time

import psutil
from PyQt5.QtWidgets import QApplication, QMessageBox

#标记

class DriveDouyin:
    @staticmethod
    def check_and_close_browser():
        for proc in psutil.process_iter(['pid', 'name']):
            if 'chrome' in proc.info['name']:
                proc.kill()

    @staticmethod
    def is_port_in_use(port):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            return s.connect_ex(('localhost', port)) == 0

    @staticmethod
    def start_server(server_ip, server_port, parameter):
        # 抖音启动的浏览器端口
        debug_port = 8989

        # 8989没打开就打开，打开了就不操作跳过
        if not DriveDouyin.is_port_in_use(debug_port):
            DriveDouyin.check_and_close_browser()
            print(debug_port)
            subprocess.run(["start", "chrome.exe", f"--remote-debugging-port={debug_port}"], shell=True)
            time.sleep(1)  # 等待浏览器完全启动

        json_str = json.dumps(parameter, ensure_ascii=False)
        print("编码前的 JSON 字符串:")
        print(json_str)
        encoded_str = base64.b64encode(json_str.encode('utf-8')).decode('utf-8')
        print("\n编码后的 Base64 字符串:")
        print(encoded_str)

        driver_params = {
            "serverIp": server_ip,
            "serverPort": server_port,
            "browserName": "chrome",
            "debugPort": 8989,
            "userDataDir": "./UserData",
            "browserPath": None,
            "argument": None,
            "extendParam": encoded_str
        }
        default_params = json.dumps(driver_params)

        # 使用 subprocess 启动 WebDriver.exe，并传递参数
        command = ["WebDriver.exe", default_params]
        process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

        # 获取进程的标准输出和错误输出
        stdout, stderr = process.communicate()

        # 处理输出
        print("Standard Output:", stdout.decode())
        print("Standard Error:", stderr.decode())


# 测试启动服务器
if __name__ == "__main__":
    server_ip = "***************"
    server_port = 9998
    parameter = {
        "key1": "value1",
        "key2": "value2"
    }
    DriveDouyin.start_server(server_ip, server_port, parameter)
