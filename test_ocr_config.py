#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试百度OCR配置的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Tool.baidu_ocr_client import get_ocr_client
from Tool.config_manager import ConfigManager

def test_ocr_config():
    """测试OCR配置"""
    print("=== 百度OCR配置测试 ===")
    
    # 测试配置管理器
    config_manager = ConfigManager()
    ocr_config = config_manager.get_ocr_config()
    print(f"当前OCR配置: {ocr_config}")
    
    # 测试OCR客户端
    ocr_client = get_ocr_client()
    print(f"OCR客户端初始化状态: {ocr_client.is_initialized()}")
    print(f"OCR服务地址: {ocr_client.server_url}")
    print(f"超时时间: {ocr_client.timeout}秒")
    
    # 测试连接
    print("\n正在测试OCR服务连接...")
    success, message = ocr_client.test_connection()
    if success:
        print(f"✅ {message}")
    else:
        print(f"❌ {message}")
    
    return success

def test_config_update():
    """测试配置更新"""
    print("\n=== 测试配置更新 ===")
    
    config_manager = ConfigManager()
    
    # 测试保存新配置
    new_config = {
        'server_url': 'http://test.example.com:5000/ocr',
        'timeout': 60
    }
    
    print(f"保存新配置: {new_config}")
    success = config_manager.save_ocr_config(new_config)
    if success:
        print("✅ 配置保存成功")
        
        # 重新加载配置验证
        reloaded_config = config_manager.get_ocr_config()
        print(f"重新加载的配置: {reloaded_config}")
        
        if reloaded_config == new_config:
            print("✅ 配置验证成功")
        else:
            print("❌ 配置验证失败")
    else:
        print("❌ 配置保存失败")

if __name__ == "__main__":
    print("开始测试百度OCR配置...")
    
    # 测试基本配置
    test_ocr_config()
    
    # 测试配置更新
    test_config_update()
    
    print("\n测试完成！")
