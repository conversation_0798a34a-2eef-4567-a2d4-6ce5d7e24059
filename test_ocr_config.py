#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试百度OCR配置的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Tool.ocr_factory import get_ocr_factory, get_ocr_engine
from Tool.config_manager import ConfigManager

def test_ocr_config():
    """测试OCR配置"""
    print("=== 百度OCR配置测试 ===")
    
    # 测试配置管理器
    config_manager = ConfigManager()
    ocr_config = config_manager.get_ocr_config()
    print(f"当前OCR配置: {ocr_config}")
    
    # 测试OCR工厂和引擎
    ocr_factory = get_ocr_factory()
    ocr_engine = get_ocr_engine()

    if ocr_engine:
        print(f"OCR引擎初始化状态: {ocr_engine.is_initialized()}")
        print(f"OCR引擎类型: {type(ocr_engine).__name__}")

        if hasattr(ocr_engine, 'server_url'):
            print(f"OCR服务地址: {ocr_engine.server_url}")
            print(f"超时时间: {ocr_engine.timeout}秒")
        elif hasattr(ocr_engine, 'client'):
            print("本地百度OCR引擎")
    else:
        print("OCR引擎未初始化")

    # 测试连接
    print("\n正在测试OCR服务连接...")
    success, message = ocr_factory.test_engine()
    if success:
        print(f"✅ {message}")
    else:
        print(f"❌ {message}")

    return success

def test_config_update():
    """测试配置更新"""
    print("\n=== 测试配置更新 ===")

    config_manager = ConfigManager()

    # 测试保存新配置
    new_config = {
        'baidu_ocr': {
            'app_id': 'test_app_id',
            'api_key': 'test_api_key',
            'secret_key': 'test_secret_key'
        },
        'remote_ocr': {
            'server_url': 'http://test.example.com:5000/ocr',
            'timeout': 60
        },
        'ocr_mode': 'remote'
    }

    print(f"保存新配置: {new_config}")
    success = config_manager.save_ocr_config(new_config)
    if success:
        print("✅ 配置保存成功")

        # 重新加载配置验证
        reloaded_config = config_manager.get_ocr_config()
        print(f"重新加载的配置: {reloaded_config}")

        if reloaded_config == new_config:
            print("✅ 配置验证成功")
        else:
            print("❌ 配置验证失败")
    else:
        print("❌ 配置保存失败")

def test_mode_switch():
    """测试模式切换"""
    print("\n=== 测试模式切换 ===")

    ocr_factory = get_ocr_factory()

    # 测试切换到本地模式
    print("切换到本地模式...")
    success = ocr_factory.switch_engine('local')
    if success:
        print("✅ 切换到本地模式成功")
    else:
        print("❌ 切换到本地模式失败")

    # 测试切换到远程模式
    print("切换到远程模式...")
    success = ocr_factory.switch_engine('remote')
    if success:
        print("✅ 切换到远程模式成功")
    else:
        print("❌ 切换到远程模式失败")

if __name__ == "__main__":
    print("开始测试百度OCR配置...")
    
    # 测试基本配置
    test_ocr_config()
    
    # 测试配置更新
    test_config_update()

    # 测试模式切换
    test_mode_switch()

    print("\n测试完成！")
