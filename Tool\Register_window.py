import sys
import os
import aiohttp
import asyncio
import base64
import logging
from PyQt5.QtWidgets import QA<PERSON><PERSON>, QMainWindow, QWidget, QVBoxLayout, QLineEdit, QPushButton, QMessageBox, QLabel
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIcon, QPixmap
from PyQt5.QtWebEngineWidgets import QWebEngineView


from Tool.auth_manager import AuthManager, site_url, admin_username, application_password, logger
from Tool.utils import add_baidu_analytics  # 百度统计

class RegisterWindow(QMainWindow):
    login_successful = pyqtSignal(str, str, str)  # 发送 username, avatar_url, role

    def __init__(self, auth_manager, login_test_enabled=False, register_test_enabled=False):
        super(RegisterWindow, self).__init__()
        self.auth_manager = auth_manager
        self.auth_manager.login_successful.connect(self.on_login_successful)  # 连接登录成功信号
        self.auth_manager.login_failed.connect(self.on_login_failed)
        self.login_test_enabled = login_test_enabled  # 是否启用登录测试按钮
        self.register_test_enabled = register_test_enabled  # 是否启用注册测试按钮
        self.initUI()

    def initUI(self):
        self.setWindowTitle('爱嘉客服-注册')
        self.setGeometry(100, 100, 300, 600)

        self.setWindowIcon(QIcon("./Static/ProgramImage/logo.ico"))

        logo_path = os.path.abspath(r'.\Static\ProgramImage\logo.png')

        logo_label = QLabel()
        if os.path.exists(logo_path):
            pixmap = QPixmap(logo_path)
            if not pixmap.isNull():
                scaled_pixmap = pixmap.scaled(200, 100, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                logo_label.setPixmap(scaled_pixmap)
                logo_label.setAlignment(Qt.AlignCenter)
                logo_label.setFixedSize(200, 100)
            else:
                logo_label.setText(f"Failed to load logo image at path: {logo_path}")
                logo_label.setAlignment(Qt.AlignCenter)
        else:
            logo_label.setText(f"Logo image not found at path: {logo_path}")
            logo_label.setAlignment(Qt.AlignCenter)

        self.username_input = QLineEdit(self)
        self.username_input.setFixedSize(200, 30)
        self.email_input = QLineEdit(self)
        self.email_input.setFixedSize(200, 30)
        self.password_input = QLineEdit(self)
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setFixedSize(200, 30)
        self.confirm_password_input = QLineEdit(self)
        self.confirm_password_input.setEchoMode(QLineEdit.Password)
        self.confirm_password_input.setFixedSize(200, 30)
        self.register_button = QPushButton('注册', self)
        self.register_button.clicked.connect(self.register)
        self.register_button.setFixedSize(200, 40)

        if self.register_test_enabled:
            self.register_test_button = QPushButton('注册测试', self)
            self.register_test_button.clicked.connect(self.quick_register_test)
            self.register_test_button.setFixedSize(200, 40)

        if self.login_test_enabled:
            self.login_test_button = QPushButton('登录测试', self)
            self.login_test_button.clicked.connect(self.quick_login_test)
            self.login_test_button.setFixedSize(200, 40)

        # 客服二维码相关代码
        wecom_kefu_path = os.path.abspath(r'.\Static\ProgramImage\wecom_kefu.png')
        wecom_kefu_label = QLabel()
        if os.path.exists(wecom_kefu_path):
            pixmap = QPixmap(wecom_kefu_path)
            if not pixmap.isNull():
                scaled_pixmap = pixmap.scaled(200, 100, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                wecom_kefu_label.setPixmap(scaled_pixmap)
                wecom_kefu_label.setAlignment(Qt.AlignCenter)
                wecom_kefu_label.setFixedSize(200, 100)
            else:
                wecom_kefu_label.setText(f"Failed to load wecom_kefu image at path: {wecom_kefu_path}")
                wecom_kefu_label.setAlignment(Qt.AlignCenter)
        else:
            wecom_kefu_label.setText(f"Wecom_kefu image not found at path: {wecom_kefu_path}")
            wecom_kefu_label.setAlignment(Qt.AlignCenter)

        qr_code_text = QLabel('扫码添加爱嘉官方客服微信', self)
        qr_code_text.setAlignment(Qt.AlignCenter)
        qr_code_text.setFixedHeight(30)

        layout = QVBoxLayout()
        layout.addWidget(logo_label)
        layout.addWidget(QLabel('用户名:', self))
        layout.addWidget(self.username_input)
        layout.addWidget(QLabel('邮箱:', self))
        layout.addWidget(self.email_input)
        layout.addWidget(QLabel('密码:', self))
        layout.addWidget(self.password_input)
        layout.addWidget(QLabel('确认密码:', self))
        layout.addWidget(self.confirm_password_input)
        layout.addWidget(self.register_button)

        if self.register_test_enabled:
            layout.addWidget(self.register_test_button)

        if self.login_test_enabled:
            layout.addWidget(self.login_test_button)

        layout.addWidget(qr_code_text)
        layout.addWidget(wecom_kefu_label)
        layout.setAlignment(Qt.AlignCenter)

        container = QWidget()
        container.setLayout(layout)
        self.setCentralWidget(container)

    def enable_login_test(self, enabled):
        self.login_test_enabled = enabled
        if enabled:
            self.login_test_button = QPushButton('登录测试', self)
            self.login_test_button.clicked.connect(self.quick_login_test)
            self.login_test_button.setFixedSize(200, 40)
        else:
            if hasattr(self, 'login_test_button'):
                self.login_test_button.deleteLater()

    def enable_register_test(self, enabled):
        self.register_test_enabled = enabled
        if enabled:
            self.register_test_button = QPushButton('注册测试', self)
            self.register_test_button.clicked.connect(self.quick_register_test)
            self.register_test_button.setFixedSize(200, 40)
        else:
            if hasattr(self, 'register_test_button'):
                self.register_test_button.deleteLater()

    def on_login_successful(self, username, avatar_url, role):
        # QMessageBox.information(self, '登录成功', f'欢迎 {username}!\n角色: {role}')
        self.login_successful.emit(username, avatar_url, role)
        self.close() # 注册成功后关闭注册窗口

    def on_login_failed(self, message):
        QMessageBox.warning(self, '登录失败', f'错误: {message}')

    async def register_async(self, username, email, password):
        url = f"{site_url}/wp-json/wp/v2/users"
        payload = {
            'username': username,
            'email': email,
            'password': password
        }
        credentials = f"{admin_username}:{application_password}"
        token = base64.b64encode(credentials.encode()).decode('utf-8')
        headers = {
            'Authorization': f'Basic {token}'
        }

        async with aiohttp.ClientSession() as session:
            try:
                async with session.post(url, json=payload, headers=headers) as response:
                    response_data = await response.json()
                    logger.debug(f"Registration response status code: {response.status}")
                    logger.debug(f"Registration response content: {response_data}")
                    if response.status == 201:
                        # 注册成功后自动登录
                        await self.auth_manager.login(username, password)
                    else:
                        error_message = response_data.get('message', '注册失败')
                        QMessageBox.warning(self, '错误', f'注册失败: {error_message}')
                        logger.error(f"Registration failed: {error_message}")
            except aiohttp.ClientError as e:
                QMessageBox.warning(self, '错误', f'注册请求失败: {str(e)}')
                logger.error(f"Registration request failed: {str(e)}")

    def register(self):
        username = self.username_input.text()
        email = self.email_input.text()
        password = self.password_input.text()
        confirm_password = self.confirm_password_input.text()

        if not username or not email or not password or not confirm_password:
            QMessageBox.warning(self, '错误', '所有字段都是必填的')
            return

        if password != confirm_password:
            QMessageBox.warning(self, '错误', '密码不匹配')
            return

        if len(password) < 8:
            QMessageBox.warning(self, '错误', '密码长度至少为8位')
            return

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(self.register_async(username, email, password))

    def quick_register_test(self):
        import uuid
        random_username = f"user_{uuid.uuid4().hex[:6]}"
        random_email = f"{random_username}@example.com"
        random_password = "TestPassword123!"

        self.username_input.setText(random_username)
        self.email_input.setText(random_email)
        self.password_input.setText(random_password)
        self.confirm_password_input.setText(random_password)

        self.register()  # 调用注册方法

    def quick_login_test(self):
        test_username = 'Sunny'
        test_password = '@u8i9o0pMK@'

        self.auth_manager.login(test_username, test_password)
        
        # 加载百度统计
        hidden_browser = QWebEngineView(self)  # 创建一个新的 QWebEngineView 实例
        hidden_browser.setFixedSize(1, 1)  # 将其大小设置为1x1像素
        hidden_browser.setVisible(False)  # 隐藏浏览器窗口
        add_baidu_analytics(hidden_browser)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    auth_manager = AuthManager()
    window = RegisterWindow(auth_manager, login_test_enabled=False, register_test_enabled=False)
    window.login_successful.connect(lambda username, avatar_url, role: print(f"Logged in user: {username}, Role: {role}, Avatar: {avatar_url}"))
    window.show()
    sys.exit(app.exec_())

# #Tool\Register_window.py
# import sys
# import os
# import requests
# import base64
# import logging
# from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QLineEdit, QPushButton, QMessageBox, QLabel
# from PyQt5.QtCore import Qt, pyqtSignal
# from PyQt5.QtGui import QIcon, QPixmap
# # 修改导入路径
# current_dir = os.path.dirname(os.path.abspath(__file__))
# parent_dir = os.path.dirname(current_dir)
# sys.path.append(parent_dir)

# from Tool.auth_manager import AuthManager, site_url, admin_username, application_password, logger

# class RegisterWindow(QMainWindow):
#     login_successful = pyqtSignal(str, str, str)  # 发送 username, avatar_url, role

#     def __init__(self, auth_manager, login_test_enabled=False, register_test_enabled=False):
#         super(RegisterWindow, self).__init__()
#         self.auth_manager = auth_manager
#         self.auth_manager.login_successful.connect(self.on_login_successful)  # 连接登录成功信号
#         self.auth_manager.login_failed.connect(self.on_login_failed)
#         self.login_test_enabled = login_test_enabled  # 是否启用登录测试按钮
#         self.register_test_enabled = register_test_enabled  # 是否启用注册测试按钮
#         self.initUI()

#     def initUI(self):
#         self.setWindowTitle('爱嘉客服-注册')
#         self.setGeometry(100, 100, 300, 600)

#         icon_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'Static', 'image', 'logo.ico'))
#         self.setWindowIcon(QIcon(icon_path))

#         logo_path = os.path.abspath(r'.\Static\ProgramImage\logo.png')

#         logo_label = QLabel()
#         if os.path.exists(logo_path):
#             pixmap = QPixmap(logo_path)
#             if not pixmap.isNull():
#                 scaled_pixmap = pixmap.scaled(200, 100, Qt.KeepAspectRatio, Qt.SmoothTransformation)
#                 logo_label.setPixmap(scaled_pixmap)
#                 logo_label.setAlignment(Qt.AlignCenter)
#                 logo_label.setFixedSize(200, 100)
#             else:
#                 logo_label.setText(f"Failed to load logo image at path: {logo_path}")
#                 logo_label.setAlignment(Qt.AlignCenter)
#         else:
#             logo_label.setText(f"Logo image not found at path: {logo_path}")
#             logo_label.setAlignment(Qt.AlignCenter)

#         self.username_input = QLineEdit(self)
#         self.username_input.setFixedSize(200, 30)
#         self.email_input = QLineEdit(self)
#         self.email_input.setFixedSize(200, 30)
#         self.password_input = QLineEdit(self)
#         self.password_input.setEchoMode(QLineEdit.Password)
#         self.password_input.setFixedSize(200, 30)
#         self.confirm_password_input = QLineEdit(self)
#         self.confirm_password_input.setEchoMode(QLineEdit.Password)
#         self.confirm_password_input.setFixedSize(200, 30)
#         self.register_button = QPushButton('注册', self)
#         self.register_button.clicked.connect(self.register)
#         self.register_button.setFixedSize(200, 40)

#         if self.register_test_enabled:
#             self.register_test_button = QPushButton('注册测试', self)
#             self.register_test_button.clicked.connect(self.quick_register_test)
#             self.register_test_button.setFixedSize(200, 40)

#         if self.login_test_enabled:
#             self.login_test_button = QPushButton('登录测试', self)
#             self.login_test_button.clicked.connect(self.quick_login_test)
#             self.login_test_button.setFixedSize(200, 40)

#         # 客服二维码相关代码
#         wecom_kefu_path = os.path.abspath(r'.\Static\ProgramImage\wecom_kefu.png')
#         wecom_kefu_label = QLabel()
#         if os.path.exists(wecom_kefu_path):
#             pixmap = QPixmap(wecom_kefu_path)
#             if not pixmap.isNull():
#                 scaled_pixmap = pixmap.scaled(200, 100, Qt.KeepAspectRatio, Qt.SmoothTransformation)
#                 wecom_kefu_label.setPixmap(scaled_pixmap)
#                 wecom_kefu_label.setAlignment(Qt.AlignCenter)
#                 wecom_kefu_label.setFixedSize(200, 100)
#             else:
#                 wecom_kefu_label.setText(f"Failed to load wecom_kefu image at path: {wecom_kefu_path}")
#                 wecom_kefu_label.setAlignment(Qt.AlignCenter)
#         else:
#             wecom_kefu_label.setText(f"Wecom_kefu image not found at path: {wecom_kefu_path}")
#             wecom_kefu_label.setAlignment(Qt.AlignCenter)

#         qr_code_text = QLabel('扫码添加爱嘉官方客服微信', self)
#         qr_code_text.setAlignment(Qt.AlignCenter)
#         qr_code_text.setFixedHeight(30)

#         layout = QVBoxLayout()
#         layout.addWidget(logo_label)
#         layout.addWidget(QLabel('用户名:', self))
#         layout.addWidget(self.username_input)
#         layout.addWidget(QLabel('邮箱:', self))
#         layout.addWidget(self.email_input)
#         layout.addWidget(QLabel('密码:', self))
#         layout.addWidget(self.password_input)
#         layout.addWidget(QLabel('确认密码:', self))
#         layout.addWidget(self.confirm_password_input)
#         layout.addWidget(self.register_button)

#         if self.register_test_enabled:
#             layout.addWidget(self.register_test_button)

#         if self.login_test_enabled:
#             layout.addWidget(self.login_test_button)

#         layout.addWidget(qr_code_text)
#         layout.addWidget(wecom_kefu_label)
#         layout.setAlignment(Qt.AlignCenter)

#         container = QWidget()
#         container.setLayout(layout)
#         self.setCentralWidget(container)

#     def enable_login_test(self, enabled):
#         self.login_test_enabled = enabled
#         if enabled:
#             self.login_test_button = QPushButton('登录测试', self)
#             self.login_test_button.clicked.connect(self.quick_login_test)
#             self.login_test_button.setFixedSize(200, 40)
#         else:
#             if hasattr(self, 'login_test_button'):
#                 self.login_test_button.deleteLater()

#     def enable_register_test(self, enabled):
#         self.register_test_enabled = enabled
#         if enabled:
#             self.register_test_button = QPushButton('注册测试', self)
#             self.register_test_button.clicked.connect(self.quick_register_test)
#             self.register_test_button.setFixedSize(200, 40)
#         else:
#             if hasattr(self, 'register_test_button'):
#                 self.register_test_button.deleteLater()

#     def on_login_successful(self, username, avatar_url, role):
#         # QMessageBox.information(self, '登录成功', f'欢迎 {username}!\n角色: {role}')
#         self.login_successful.emit(username, avatar_url, role)
#         self.close() # 注册成功后关闭注册窗口

#     def on_login_failed(self, message):
#         QMessageBox.warning(self, '登录失败', f'错误: {message}')

#     def register(self):
#         username = self.username_input.text()
#         email = self.email_input.text()
#         password = self.password_input.text()
#         confirm_password = self.confirm_password_input.text()

#         if not username or not email or not password or not confirm_password:
#             QMessageBox.warning(self, '错误', '所有字段都是必填的')
#             return

#         if password != confirm_password:
#             QMessageBox.warning(self, '错误', '密码不匹配')
#             return

#         if len(password) < 8:
#             QMessageBox.warning(self, '错误', '密码长度至少为8位')
#             return

#         url = f"{site_url}/wp-json/wp/v2/users"
#         payload = {
#             'username': username,
#             'email': email,
#             'password': password
#         }
#         credentials = f"{admin_username}:{application_password}"
#         token = base64.b64encode(credentials.encode()).decode('utf-8')
#         headers = {
#             'Authorization': f'Basic {token}'
#         }

#         try:
#             response = requests.post(url, json=payload, headers=headers)
#             logger.debug(f"Registration response status code: {response.status_code}")
#             logger.debug(f"Registration response content: {response.content.decode()}")
#             if response.status_code == 201:
#                 # 注册成功后自动登录
#                 self.auth_manager.login(username, password)
#             else:
#                 error_message = response.json().get('message', '注册失败')
#                 QMessageBox.warning(self, '错误', f'注册失败: {error_message}')
#                 logger.error(f"Registration failed: {error_message}")
#         except requests.exceptions.RequestException as e:
#             QMessageBox.warning(self, '错误', f'注册请求失败: {str(e)}')
#             logger.error(f"Registration request failed: {str(e)}")

#     def quick_register_test(self):
#         import uuid
#         random_username = f"user_{uuid.uuid4().hex[:6]}"
#         random_email = f"{random_username}@example.com"
#         random_password = "TestPassword123!"

#         self.username_input.setText(random_username)
#         self.email_input.setText(random_email)
#         self.password_input.setText(random_password)
#         self.confirm_password_input.setText(random_password)

#         self.register()  # 调用注册方法

#     def quick_login_test(self):
#         test_username = 'Sunny'
#         test_password = '@u8i9o0pMK@'

#         self.auth_manager.login(test_username, test_password)

# if __name__ == '__main__':
#     app = QApplication(sys.argv)
#     auth_manager = AuthManager()
#     window = RegisterWindow(auth_manager, login_test_enabled=True, register_test_enabled=True)
#     window.login_successful.connect(lambda username, avatar_url, role: print(f"Logged in user: {username}, Role: {role}, Avatar: {avatar_url}"))
#     window.show()
#     sys.exit(app.exec_())


