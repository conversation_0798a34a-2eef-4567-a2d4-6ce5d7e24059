import sys
import time
import json
import yaml
from typing import Dict, Any
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QPushButton, QDialog, QCheckBox, QLabel, QSizePolicy, QHBoxLayout, QLineEdit, QTableWidget, QTableWidgetItem
from PyQt5.QtCore import QThread, pyqtSignal, Qt
from PyQt5.QtGui import QIcon
from Tool.JDmultiwebdriver import WebDriver #pdd适用
from Tool.ClietServer import ServerAgency


class douyinMultiInstanceDialog(QDialog):
    proxy_configs = { # ggg
        1: ('127.0.5.1', 8651),
        2: ('127.0.5.2', 8652),
        3: ('127.0.5.3', 8653),
        4: ('127.0.5.4', 8654),
        5: ('127.0.5.5', 8655),
        6: ('127.0.5.6', 8656),
        7: ('127.0.5.7', 8657),
        8: ('127.0.5.8', 8658)
    }

    def __init__(self, states, shop_names, web_drivers, proxy_servers, parent=None):
        super().__init__(parent)
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowContextHelpButtonHint)
        self.setWindowTitle('抖音多开控制面板')
        self.setWindowIcon(QIcon("./Static/ProgramImage/logo.ico"))
        self.setFixedSize(600, 400)

        self.main_layout = QVBoxLayout(self)
        self.checkboxes = []
        self.shop_name_edits = []
        self.web_drivers = web_drivers
        self.proxy_servers = proxy_servers
        self.base_debug_port = 8951 # ggg
        # 存储待启动的实例索引
        self.pending_instances = []
        merged_config = self.merge_configs('Tool/serverConfiguration.yaml', 'Tool/config.yaml', 'Tool/config.json')
        print("初始中获取的配置是：", merged_config)
        checkbox_layout = QVBoxLayout()
        self.bind_buttons = []  # 添加一个存储绑定店铺按钮的列表
        for i in range(1, 9):
            checkbox_hbox = QHBoxLayout()
            checkbox = QCheckBox(f'抖音多开{i}', self)
            # 修改这里，不再连接toggle_instance，而是连接到update_pending_instances
            checkbox.stateChanged.connect(self.update_pending_instances)
            self.checkboxes.append(checkbox)
            checkbox_hbox.addWidget(checkbox)
            
            # 添加绑定账号按钮
            bind_button = QPushButton('绑定浏览器', self)
            bind_button.setFixedSize(80, 26)
            bind_button.setStyleSheet("""
                QPushButton {
                    background-color: #4CAF50;
                    color: white;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #45a049;
                }
                QPushButton:pressed {
                    background-color: #3e8e41;
                }
            """)
            # 设置按钮索引，用于识别是哪个实例的按钮
            bind_button.setProperty("instance_index", i)
            bind_button.clicked.connect(self.bind_and_start_instance)
            self.bind_buttons.append(bind_button)
            checkbox_hbox.addWidget(bind_button)

            shop_name_edit = QLineEdit(self)
            shop_name_edit.setFixedSize(180, 26)

            shop_name_edit.setReadOnly(True)
            self.shop_name_edits.append(shop_name_edit)
            checkbox_hbox.addWidget(shop_name_edit)

            checkbox_layout.addLayout(checkbox_hbox)

        hbox = QHBoxLayout()
        checkbox_container = QWidget()
        checkbox_container.setLayout(checkbox_layout)
        hbox.addWidget(checkbox_container)

        explanation_label = QLabel('① 首次需要"绑定浏览器"在弹出窗口选"不登陆—跳过—不设置"然后关闭即可\n\n'
            '② 勾选店铺/账号，点击"启动多开"扫码登录店铺即可\n\n',
            self)
        explanation_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        explanation_label.setWordWrap(True)
        explanation_label.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        explanation_label.setStyleSheet(
            "font-size: 12pt; color: #333; background-color: #f0f0f0; padding: 10px; border-radius: 5px;")
        hbox.addWidget(explanation_label)

        hbox.setStretch(0, 1)
        hbox.setStretch(1, 2)
        self.main_layout.addLayout(hbox)

        self.complete_button = QPushButton('启动多开', self)
        self.complete_button.setFixedSize(100, 35)  # 调整尺寸为100x35
        self.complete_button.setStyleSheet("""
            QPushButton {
                background-color: qlineargradient(
                    spread:pad, x1:0, y1:0, x2:1, y2:1, 
                    stop:0 rgba(85, 170, 255, 255), 
                    stop:1 rgba(0, 85, 255, 255)
                );  /* 使用渐变色背景 */
                color: white;  /* 白色字体 */
                font-size: 11pt;  /* 字体稍微小一点 */
                font-weight: bold;  /* 字体加粗 */
                border-radius: 10px;  /* 圆角边框 */
                border: 2px solid #0055ff;  /* 蓝色边框 */
                padding: 5px;  /* 增加内边距 */
            }
            QPushButton:hover {
                background-color: qlineargradient(
                    spread:pad, x1:0, y1:0, x2:1, y2:1, 
                    stop:0 rgba(0, 85, 255, 255), 
                    stop:1 rgba(85, 170, 255, 255)
                );  /* 鼠标悬停时反转渐变色 */
            }
            QPushButton:pressed {
                background-color: #0044cc;  /* 按下时变为深蓝色 */
            }
        """)
        # 修改按钮点击事件，连接到启动实例的方法
        self.complete_button.clicked.connect(self.start_pending_instances)
        self.main_layout.addWidget(self.complete_button, alignment=Qt.AlignCenter)

        self.load_state(states, shop_names)
        for i in range(1, 9):
            shop_key = f'抖音多开{i}'
            # 检查是否存在该shop_key，避免KeyError
            if shop_key in merged_config:
                shop_name = merged_config[shop_key].get('Shop Name', '')
                print(f"正在获取 {shop_key}，用户名称为：{shop_name}")
                self.shop_name_edits[i - 1].setText(f"用户名称：{shop_name}")
            else:
                print(f"{shop_key} 不存在于配置中")

    def merge_configs(self, server_config_path: str, config_yaml_path: str, config_json_path: str,
                      duokaidict: Dict[str, Any] = {}) -> Dict[str, Any]:
        def load_yaml(file_path: str) -> Dict[str, Any]:
            try:
                with open(file_path, 'r', encoding='utf-8') as file:
                    content = yaml.safe_load(file)
                    if isinstance(content, list):
                        content = {str(index): value for index, value in enumerate(content)}
                    elif not isinstance(content, dict):
                        raise TypeError(f"{file_path} content is neither a dictionary nor a list")
                    return content
            except FileNotFoundError:
                print(f"File {file_path} not found.")
                return {}
            except yaml.YAMLError as e:
                print(f"Error loading YAML file {file_path}: {e}")
                return {}

        def load_json(file_path: str) -> Dict[str, Any]:
            config_dict = {}
            try:
                with open(file_path, 'r', encoding='utf-8') as file:
                    configs = json.load(file)
                    if not configs:
                        print("Configuration file is empty.")
                        return config_dict

                    for config in configs:
                        config_name = config.get('name', 'N/A')
                        config_dict[config_name] = {
                            "Shop Name": config.get('shop_name', 'N/A'),
                            "API Base": config.get('api_base', 'N/A'),
                            "Key": config.get('key', 'N/A'),
                            "Customer Service Name": config.get('customer_service_name', 'N/A')
                        }
                    return config_dict
            except FileNotFoundError:
                print(f"File {file_path} not found.")
                return config_dict
            except json.JSONDecodeError as e:
                print(f"Error parsing JSON file {file_path}: {e}")
                return config_dict

        server_config = load_yaml(server_config_path)
        config_yaml = load_yaml(config_yaml_path)
        config_json = load_json(config_json_path)

        merged_config = {**server_config, **config_yaml, **config_json, **duokaidict}

        return merged_config

    def update_pending_instances(self, state):
        checkbox = self.sender()
        instance_index = self.checkboxes.index(checkbox) + 1
        if state == Qt.Checked:
            if instance_index not in self.pending_instances:
                self.pending_instances.append(instance_index)
        else:
            if instance_index in self.pending_instances:
                self.pending_instances.remove(instance_index)

    def start_pending_instances(self):
        for instance_index in self.pending_instances:
            self.start_instance(instance_index)
        self.accept()  # 启动完成后关闭对话框

    def start_instance(self, instance_index):
        duokaidict = {"duokaikey": f'抖音多开{instance_index}'}
        merged_config = self.merge_configs('Tool/serverConfiguration.yaml', 'Tool/config.yaml', 'Tool/config.json',
                                           duokaidict)
        print("获取的配置是：", merged_config)

        webdriver_ip = merged_config['server']['WebDriverServer_IP']
        print("获取到的抖音服务器ip是：", webdriver_ip)

        target_host_PDD = webdriver_ip
        target_port_PDD = 9998 # ggg
        proxy_host_PDD, proxy_port_PDD = self.proxy_configs[instance_index]
        PDD_debug_port = self.base_debug_port + instance_index - 1
        user_data_dir = f"./UserData/DY/{instance_index}" # ggg

        parameter = merged_config

        if instance_index not in self.web_drivers:
            # 创建并启动代理服务器
            proxy_server = ServerAgency(target_host_PDD, target_port_PDD, proxy_host_PDD,
                                        proxy_port_PDD)
            self.proxy_servers[instance_index] = proxy_server

            # 创建并启动WebDriver
            web_driver = WebDriver(proxy_host_PDD, proxy_port_PDD, parameter, PDD_debug_port, user_data_dir)
            self.web_drivers[instance_index] = web_driver

            # 启动WebDriver和代理服务器
            try:
                web_driver.start()
                self.proxy_servers[instance_index].ClietServerMain()
            except Exception as e:
                print(f"启动实例 {instance_index} 时出错：{e}")
        else:

            # 重新创建并启动WebDriver
            web_driver = WebDriver(proxy_host_PDD, proxy_port_PDD, parameter, PDD_debug_port, user_data_dir)
            self.web_drivers[instance_index] = web_driver

            try:
                web_driver.start()
                self.proxy_servers[instance_index].ClietServerMain()
            except Exception as e:
                print(f"启动实例 {instance_index} 时出错：{e}")

    def stop_instance(self, instance_index):
        try:
            self.web_drivers[instance_index].stop()
            self.proxy_servers[instance_index].ServerClose()
        except Exception as e:
            self.update_log(f"停止实例 {instance_index} 时发生异常: {e}")

    def update_log(self, message):
        print(message)

    def bind_and_start_instance(self):
        # 获取触发事件的按钮
        sender = self.sender()
        # 获取按钮对应的实例索引
        instance_index = sender.property("instance_index")
        
        # 直接启动该实例
        self.start_instance(instance_index)
        
        # 不改变按钮状态和文本，始终保持"绑定浏览器"

    def save_state(self):
        states = [checkbox.isChecked() for checkbox in self.checkboxes]
        shop_names = [edit.text() for edit in self.shop_name_edits]
        return states, shop_names

    def load_state(self, states, shop_names):
        for checkbox, checked in zip(self.checkboxes, states):
            checkbox.setChecked(checked)
        for edit, shop_name in zip(self.shop_name_edits, shop_names):
            edit.setText(shop_name)

    def complete(self):
        self.accept()

    def setup_ui(self):
        # 创建表格
        self.table = QTableWidget(self)
        self.table.setColumnCount(4)
        self.table.setHorizontalHeaderLabels(["店铺名称", "API Base", "Key", "状态"])
        # 隐藏API Base列
        self.table.setColumnHidden(1, True)
        
        # 设置表格内容
        for i in range(8):
            self.table.insertRow(i)
            # 店铺名称
            name_item = QTableWidgetItem(self.shop_names[i])
            self.table.setItem(i, 0, name_item)
            
            # API Base (隐藏并写死)
            api_base_item = QTableWidgetItem("http://api.aijiakefu.com/v1")
            self.table.setItem(i, 1, api_base_item)
            
            # Key
            key_item = QTableWidgetItem("")
            self.table.setItem(i, 2, key_item)
            
            # 状态
            status_item = QTableWidgetItem("未启动")
            self.table.setItem(i, 3, status_item)
