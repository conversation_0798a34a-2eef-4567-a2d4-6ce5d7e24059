import sys
from PyQt5.QtGui import QIcon
from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QTableWidget,
                             QTableWidgetItem, QHeaderView, QAbstractItemView,
                             QPushButton, QMessageBox, QHBoxLayout)
import json
import os

class MultiInstanceControlWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()

    def initUI(self):
        self.setWindowTitle("多开配置面板")
        self.setWindowIcon(QIcon("./Static/ProgramImage/logo.ico"))
        self.setGeometry(100, 100, 600, 400)

        self.layout = QVBoxLayout()

        self.table = QTableWidget(0, 5)  # 增加列数为5
        self.table.setHorizontalHeaderLabels(["配置名称", "店铺名称", "API Base", "Key", "人工客服名称"])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.table.setSelectionBehavior(QAbstractItemView.SelectRows)
        # 隐藏API Base列
        self.table.setColumnHidden(2, True)

        self.layout.addWidget(self.table)

        # 添加"添加配置"按钮
        self.add_button = QPushButton("添加配置")
        self.add_button.setFixedSize(100, 35)
        self.add_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-size: 11pt;
                font-weight: bold;
                border-radius: 10px;
                border: 2px solid #4CAF50;
                padding: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #388e3c;
            }
        """)
        self.add_button.clicked.connect(self.add_empty_config)  # 连接按钮功能

        self.save_button = QPushButton("保存配置")
        self.save_button.setFixedSize(100, 35)  # 调整尺寸为100x35
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: qlineargradient(
                    spread:pad, x1:0, y1:0, x2:1, y2:1, 
                    stop:0 rgba(85, 170, 255, 255), 
                    stop:1 rgba(0, 85, 255, 255)
                );  /* 使用渐变色背景 */
                color: white;  /* 白色字体 */
                font-size: 11pt;  /* 字体稍微小一点 */
                font-weight: bold;  /* 字体加粗 */
                border-radius: 10px;  /* 圆角边框 */
                border: 2px solid #0055ff;  /* 蓝色边框 */
                padding: 5px;  /* 增加内边距 */
            }
            QPushButton:hover {
                background-color: qlineargradient(
                    spread:pad, x1:0, y1:0, x2:1, y2:1, 
                    stop:0 rgba(0, 85, 255, 255), 
                    stop:1 rgba(85, 170, 255, 255)
                );  /* 鼠标悬停时反转渐变色 */
            }
            QPushButton:pressed {
                background-color: #0044cc;  /* 按下时变为深蓝色 */
            }
        """)
        self.save_button.clicked.connect(self.save_configs)

        # 创建一个水平布局用于按钮居中
        button_layout = QHBoxLayout()
        button_layout.addStretch(1)  # 左侧弹性空间
        button_layout.addWidget(self.add_button)  # 添加"添加配置"按钮
        button_layout.addWidget(self.save_button)  # 添加"保存配置"按钮
        button_layout.addStretch(1)  # 右侧弹性空间

        self.layout.addLayout(button_layout)
        self.setLayout(self.layout)

        self.load_configs()

    def add_empty_config(self):
        # 调用add_config方法，添加一个新的空配置行
        self.add_config("新配置")

    def add_config(self, config_name, shop_name="", api_base="", key="", customer_service_name=""):
        row_position = self.table.rowCount()
        self.table.insertRow(row_position)
        # 所有列均可编辑
        self.table.setItem(row_position, 0, QTableWidgetItem(config_name))
        self.table.setItem(row_position, 1, QTableWidgetItem(shop_name))
        # API Base写死
        self.table.setItem(row_position, 2, QTableWidgetItem("http://api.aijiakefu.com/v1"))
        self.table.setItem(row_position, 3, QTableWidgetItem(key))
        self.table.setItem(row_position, 4, QTableWidgetItem(customer_service_name))

    def load_configs(self):
        if os.path.exists("Tool/config.json"):
            with open("Tool/config.json", 'r', encoding='utf-8') as file:
                configs = json.load(file)
                for config in configs:
                    config_name = config.get("name", f"抖店多开{self.table.rowCount() + 1}")
                    shop_name = config.get("shop_name", "")
                    # API Base写死
                    self.add_config(config_name, shop_name, "http://api.aijiakefu.com/v1", config.get("key", ""), config.get("customer_service_name", ""))
        else:
            for i in range(8):
                config_name = f"抖店多开{i + 1}"
                self.add_config(config_name)

    def save_configs(self):
        config_list = []
        for row in range(self.table.rowCount()):
            config = {
                "name": self.table.item(row, 0).text(),
                "shop_name": self.table.item(row, 1).text(),  # 店铺名称
                # API Base写死
                "api_base": "http://api.aijiakefu.com/v1",
                "key": self.table.item(row, 3).text(),
                "customer_service_name": self.table.item(row, 4).text()  # 人工客服名称
            }
            config_list.append(config)

        with open("Tool/config.json", 'w', encoding='utf-8') as file:
            json.dump(config_list, file, ensure_ascii=False, indent=4)

        QMessageBox.information(self, "提示", "配置已保存")

    def read_configs_to_dict(self):
        config_file = "Tool/config.json"
        config_dict = {}

        if not os.path.exists(config_file):
            print(f"{config_file} 文件不存在.")
            return config_dict

        try:
            with open(config_file, 'r', encoding='utf-8') as file:
                configs = json.load(file)
                if not configs:
                    print("配置文件为空.")
                    return config_dict

                for config in configs:
                    config_name = config.get('name', 'N/A')
                    config_dict[config_name] = {
                        "Shop Name": config.get('shop_name', 'N/A'),  # 店铺名称
                        # API Base写死
                        "API Base": "http://api.aijiakefu.com/v1",
                        "Key": config.get('key', 'N/A'),
                        "Customer Service Name": config.get('customer_service_name', 'N/A')  # 人工客服名称
                    }
        except json.JSONDecodeError as e:
            print(f"读取JSON文件时发生错误: {e}")

        return config_dict


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MultiInstanceControlWindow()
    window.show()
    sys.exit(app.exec_())
