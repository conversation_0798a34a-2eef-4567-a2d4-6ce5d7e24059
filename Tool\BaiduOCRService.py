#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
百度OCR服务
提供HTTP API接口用于OCR识别
"""

from flask import Flask, request, jsonify
import base64
import json
import logging
import os
import sys

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config_manager import ConfigManager

app = Flask(__name__)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局OCR客户端
ocr_client = None

def init_ocr_client():
    """初始化OCR客户端"""
    global ocr_client
    try:
        from aip import AipOcr
        
        config_manager = ConfigManager()
        ocr_config = config_manager.get_ocr_config()
        baidu_config = ocr_config.get('baidu_ocr', {})
        
        app_id = baidu_config.get('app_id')
        api_key = baidu_config.get('api_key')
        secret_key = baidu_config.get('secret_key')
        
        if not all([app_id, api_key, secret_key]):
            logger.error("百度OCR配置不完整，请检查配置文件")
            return False
        
        ocr_client = AipOcr(app_id, api_key, secret_key)
        logger.info("百度OCR客户端初始化成功")
        return True
        
    except ImportError:
        logger.error("缺少百度OCR SDK，请安装: pip install baidu-aip")
        return False
    except Exception as e:
        logger.error(f"初始化百度OCR客户端失败: {str(e)}")
        return False

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    if ocr_client is None:
        return jsonify({
            'status': 'error',
            'message': 'OCR客户端未初始化'
        }), 500
    
    return jsonify({
        'status': 'ok',
        'message': '百度OCR服务正常运行'
    })

@app.route('/ocr', methods=['POST'])
def ocr_recognize():
    """OCR识别接口"""
    try:
        if ocr_client is None:
            return jsonify({
                'success': False,
                'error': 'OCR客户端未初始化'
            }), 500
        
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请求数据为空'
            }), 400
        
        image_data = data.get('image')
        image_format = data.get('format', 'base64')
        ocr_type = data.get('type', 'accurate')
        
        if not image_data:
            return jsonify({
                'success': False,
                'error': '缺少图片数据'
            }), 400
        
        # 处理base64图片数据
        if image_format == 'base64':
            try:
                image_bytes = base64.b64decode(image_data)
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': f'base64解码失败: {str(e)}'
                }), 400
        else:
            return jsonify({
                'success': False,
                'error': '不支持的图片格式'
            }), 400
        
        # 调用百度OCR API
        if ocr_type == 'accurate':
            result = ocr_client.accurateBasic(image_bytes)
        else:
            result = ocr_client.basicGeneral(image_bytes)
        
        # 处理识别结果
        if 'words_result' in result:
            texts = [item['words'] for item in result['words_result']]
            return jsonify({
                'success': True,
                'texts': texts,
                'count': len(texts)
            })
        else:
            error_msg = result.get('error_msg', '识别失败')
            logger.warning(f"百度OCR识别失败: {error_msg}")
            return jsonify({
                'success': False,
                'error': error_msg
            }), 500
            
    except Exception as e:
        logger.error(f"OCR识别异常: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'服务器内部错误: {str(e)}'
        }), 500

@app.route('/config', methods=['GET'])
def get_config():
    """获取配置信息"""
    try:
        config_manager = ConfigManager()
        ocr_config = config_manager.get_ocr_config()
        baidu_config = ocr_config.get('baidu_ocr', {})
        
        # 隐藏敏感信息
        safe_config = {
            'app_id': baidu_config.get('app_id', '未配置'),
            'api_key_configured': bool(baidu_config.get('api_key')),
            'secret_key_configured': bool(baidu_config.get('secret_key')),
            'ocr_mode': ocr_config.get('ocr_mode', 'local')
        }
        
        return jsonify({
            'success': True,
            'config': safe_config
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def main():
    """主函数"""
    logger.info("启动百度OCR服务...")
    
    # 初始化OCR客户端
    if not init_ocr_client():
        logger.error("OCR客户端初始化失败，服务无法启动")
        return False
    
    # 启动Flask服务
    try:
        app.run(host='0.0.0.0', port=5000, debug=False)
        logger.info("百度OCR服务启动成功，监听端口: 5000")
        return True
    except Exception as e:
        logger.error(f"启动Flask服务失败: {str(e)}")
        return False

if __name__ == '__main__':
    success = main()
    if not success:
        sys.exit(1)
