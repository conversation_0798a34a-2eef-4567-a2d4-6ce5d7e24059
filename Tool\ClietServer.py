# Tool\ClietServer.py
import os

from PyQt5.QtWidgets import <PERSON>A<PERSON><PERSON>, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QMessageBox, QGroupBox, QRadioButton
from PyQt5.QtGui import QColor, QPalette, QIcon
import yaml
import socket
import threading
from Tool.config_manager import ConfigManager
from Tool.baidu_ocr_client import get_ocr_client


class ServerConfigManager:#服务器配置
    def __init__(self):
        self.config_file = 'Tool/serverConfiguration.yaml'
        self.server_config_section = "server"
        self.server_config = {
            "WINDOWS_DRIVER_SERVER_IP": "localhost",
            "WINDOWS_DRIVER_SERVER_PORT": 9999,
            "WebDriverServer_IP": "localhost",
            "WebDriverServer_PORT": 9998,
            "OCR_SERVER_IP": "localhost"
        }
        self.load_config()

    def get_server_config(self):
        return self.server_config

    def update_server_config(self, key, value):
        self.server_config[key] = value

    def save_config(self):
        data = {}
        if os.path.exists(self.config_file):
            with open(self.config_file, 'r', encoding='utf-8') as file:
                data = yaml.safe_load(file) or {}

        data[self.server_config_section] = self.server_config

        with open(self.config_file, 'w', encoding='utf-8') as file:
            yaml.safe_dump(data, file, allow_unicode=True)
        print("Config saved:", self.server_config)

    def load_config(self):
        if os.path.exists(self.config_file):
            with open(self.config_file, 'r', encoding='utf-8') as file:
                data = yaml.safe_load(file) or {}
                if self.server_config_section in data:
                    self.server_config = data[self.server_config_section]
        else:
            self.save_config()
class ServerAgency:
    def __init__(self, target_host=None, target_port=None, proxy_host=None, proxy_port=None):
        self.config_manager = ConfigManager()  # 使用ConfigManager类来管理配置
        server_config = self.config_manager.get_server_config()

        # Web驱动服务器配置
        self.TARGET_HOST = target_host if target_host else server_config["WebDriverServer_IP"]
        self.TARGET_PORT = target_port if target_port else server_config["WebDriverServer_PORT"]

        # Windows驱动服务器配置
        self.PROXY_HOST = proxy_host if proxy_host else server_config["WINDOWS_DRIVER_SERVER_IP"]
        self.PROXY_PORT = proxy_port if proxy_port else server_config["WINDOWS_DRIVER_SERVER_PORT"]

        if self.PROXY_HOST and self.PROXY_PORT:
            # 创建代理服务器的监听套接字
            self.proxy_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.proxy_socket.bind((self.PROXY_HOST, self.PROXY_PORT))
            self.proxy_socket.listen(5)
            print(f"[*] Listening on {self.PROXY_HOST}:{self.PROXY_PORT}")

    def handle_client(self, client_socket):
        # 创建连接到目标服务器的套接字
        self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.server_socket.connect((self.TARGET_HOST, self.TARGET_PORT))

        while True:
            # 接收来自目标服务器的响应数据
            response = self.server_socket.recv(4096)

            print("[*] 目标服务器连接成功！Received response from server:")
            print(response.decode())

            # 将响应数据转发给客户端
            client_socket.sendall(response)

            # 接收来自客户端的请求数据
            request = client_socket.recv(4096*1024)
            print(request.decode())

            self.server_socket.sendall(request)

    def ClietServerStart(self):
        while True:
            # 接受来自客户端的连接以验证服务器可用性
            self.client_socket, self.addr = self.proxy_socket.accept()
            print(f"[*] 服务器连接成功！Accepted connection from {self.addr}")

            # 创建一个新的线程来处理客户端请求
            client_handler = threading.Thread(target=self.handle_client, args=(self.client_socket,))
            client_handler.start()

    def ServerClose(self):
        try:
            self.server_socket.close()
            self.client_socket.close()
        except Exception as e:
            pass

    def ClietServerMain(self):
        # 创建一个新的线程来处理客户端请求
        client_main = threading.Thread(target=self.ClietServerStart, args=())
        client_main.daemon = True
        client_main.start()

def check_port_available(ip, port):
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)  # 设置连接尝试的超时时间
            result = s.connect_ex((ip, port))
            if result == 0:
                print(f"Port {port} on {ip} is open")
                return True
            else:
                print(f"Port {port} on {ip} is closed")
                return False
    except socket.error as e:
        print(f"Could not connect to {ip}:{port} - {e}")
        return False

class ProxyConfigPage(QWidget):#服务器配置页面
    def __init__(self):
        super().__init__()
        self.initUI()


    def initUI(self):
        self.setWindowTitle('爱嘉客服-服务器设置')
        self.setWindowIcon(QIcon("./Static/ProgramImage/logo.ico"))
        layout = QVBoxLayout()

        self.config_manager = ServerConfigManager()
        self.ocr_config_manager = ConfigManager()  # 用于OCR配置

        # Windows驱动服务器配置区域
        windows_group = QGroupBox("Windows驱动服务器配置")
        windows_layout = QVBoxLayout()

        self.windows_target_host_edit = QLineEdit()
        self.windows_target_host_edit.setText(self.config_manager.get_server_config()["WINDOWS_DRIVER_SERVER_IP"])
        self.windows_target_host_edit.setEnabled(True)  # 设置为可编辑
        self.windows_target_port_edit = QLineEdit()
        self.windows_target_port_edit.setText(str(self.config_manager.get_server_config()["WINDOWS_DRIVER_SERVER_PORT"]))
        self.windows_target_port_edit.setEnabled(False)  # 设置为可编辑

        self.windows_proxy_host_edit = QLineEdit()
        self.windows_proxy_host_edit.setText('127.0.0.1')
        self.windows_proxy_host_edit.setEnabled(False)  # 设置为不可编辑
        self.windows_proxy_port_edit = QLineEdit()
        self.windows_proxy_port_edit.setText('8888')
        self.windows_proxy_port_edit.setEnabled(False)  # 设置为不可编辑

        self.windows_status_label = QLabel()
        self.windows_status_label.setAutoFillBackground(True)
        self.update_status_label(self.windows_status_label, self.windows_target_host_edit.text(), int(self.windows_target_port_edit.text()))

        self.windows_connect_button = QPushButton("验证服务器可用性")
        self.windows_connect_button.setFixedSize(150, 40)  # 设置固定大小
        self.windows_connect_button.clicked.connect(lambda: self.test_server_status(self.windows_status_label, self.windows_target_host_edit.text(), int(self.windows_target_port_edit.text())))
        self.windows_connect_button.setEnabled(True)  # 设置为不可用

        windows_button_layout = QHBoxLayout()
        windows_button_layout.addStretch()  # 添加伸缩项以居中按钮
        windows_button_layout.addWidget(self.windows_connect_button)
        windows_button_layout.addStretch()  # 添加伸缩项以居中按钮

        windows_layout.addLayout(self.create_horizontal_layout("目标服务器主机:", self.windows_target_host_edit, "端口:", self.windows_target_port_edit))
        windows_layout.addLayout(self.create_horizontal_layout("代理服务器主机:", self.windows_proxy_host_edit, "端口:", self.windows_proxy_port_edit))
        windows_layout.addWidget(self.windows_status_label)
        windows_layout.addLayout(windows_button_layout)

        windows_group.setLayout(windows_layout)
        layout.addWidget(windows_group)

        # Web驱动服务器配置区域
        web_group = QGroupBox("Web驱动服务器配置")
        web_layout = QVBoxLayout()

        self.web_target_host_edit = QLineEdit()
        self.web_target_host_edit.setText(self.config_manager.get_server_config()["WebDriverServer_IP"])
        self.web_target_host_edit.setEnabled(True)  # 设置为可编辑
        self.web_target_port_edit = QLineEdit()
        self.web_target_port_edit.setText(str(self.config_manager.get_server_config()["WebDriverServer_PORT"]))
        # self.web_target_port_edit.setEnabled(True)  # 设置为可编辑
        self.web_target_port_edit.setEnabled(False)

        self.web_proxy_host_edit = QLineEdit()
        self.web_proxy_host_edit.setText('*********')
        self.web_proxy_host_edit.setEnabled(False)  # 设置为不可编辑
        self.web_proxy_port_edit = QLineEdit()
        self.web_proxy_port_edit.setText('8881')
        self.web_proxy_port_edit.setEnabled(False)  # 设置为不可编辑

        self.web_status_label = QLabel()
        self.web_status_label.setAutoFillBackground(True)
        self.update_status_label(self.web_status_label, self.web_target_host_edit.text(), int(self.web_target_port_edit.text()))

        self.web_connect_button = QPushButton("验证服务器可用性")
        self.web_connect_button.setFixedSize(150, 40)  # 设置固定大小
        self.web_connect_button.clicked.connect(lambda: self.test_server_status(self.web_status_label, self.web_target_host_edit.text(), int(self.web_target_port_edit.text())))
        self.web_connect_button.setEnabled(True)  # 设置为不可用

        web_button_layout = QHBoxLayout()
        web_button_layout.addStretch()  # 添加伸缩项以居中按钮
        web_button_layout.addWidget(self.web_connect_button)
        web_button_layout.addStretch()  # 添加伸缩项以居中按钮

        web_layout.addLayout(self.create_horizontal_layout("目标服务器主机:", self.web_target_host_edit, "端口:", self.web_target_port_edit))
        web_layout.addLayout(self.create_horizontal_layout("代理服务器主机:", self.web_proxy_host_edit, "端口:", self.web_proxy_port_edit))
        web_layout.addWidget(self.web_status_label)
        web_layout.addLayout(web_button_layout)

        web_group.setLayout(web_layout)
        layout.addWidget(web_group)

        # 百度OCR服务配置区域
        ocr_group = QGroupBox("百度OCR服务配置")
        ocr_layout = QVBoxLayout()

        # 获取OCR配置
        ocr_config = self.ocr_config_manager.get_ocr_config()

        # OCR模式选择
        mode_layout = QHBoxLayout()
        mode_layout.addWidget(QLabel("OCR模式:"))

        self.ocr_mode_local = QRadioButton("本地百度OCR")
        self.ocr_mode_remote = QRadioButton("远程OCR服务")

        current_mode = ocr_config.get("ocr_mode", "remote")
        if current_mode == "local":
            self.ocr_mode_local.setChecked(True)
        else:
            self.ocr_mode_remote.setChecked(True)

        self.ocr_mode_local.toggled.connect(self.on_ocr_mode_changed)
        self.ocr_mode_remote.toggled.connect(self.on_ocr_mode_changed)

        mode_layout.addWidget(self.ocr_mode_local)
        mode_layout.addWidget(self.ocr_mode_remote)
        mode_layout.addStretch()

        ocr_layout.addLayout(mode_layout)

        # 本地百度OCR配置
        self.local_ocr_group = QGroupBox("本地百度OCR配置")
        local_ocr_layout = QVBoxLayout()

        baidu_config = ocr_config.get("baidu_ocr", {})

        self.baidu_app_id_edit = QLineEdit()
        self.baidu_app_id_edit.setText(baidu_config.get("app_id", ""))
        self.baidu_app_id_edit.setPlaceholderText("请输入百度OCR APP_ID")

        self.baidu_api_key_edit = QLineEdit()
        self.baidu_api_key_edit.setText(baidu_config.get("api_key", ""))
        self.baidu_api_key_edit.setPlaceholderText("请输入百度OCR API_KEY")

        self.baidu_secret_key_edit = QLineEdit()
        self.baidu_secret_key_edit.setText(baidu_config.get("secret_key", ""))
        self.baidu_secret_key_edit.setPlaceholderText("请输入百度OCR SECRET_KEY")
        self.baidu_secret_key_edit.setEchoMode(QLineEdit.Password)

        local_ocr_layout.addLayout(self.create_horizontal_layout("APP_ID:", self.baidu_app_id_edit))
        local_ocr_layout.addLayout(self.create_horizontal_layout("API_KEY:", self.baidu_api_key_edit))
        local_ocr_layout.addLayout(self.create_horizontal_layout("SECRET_KEY:", self.baidu_secret_key_edit))

        self.local_ocr_group.setLayout(local_ocr_layout)
        ocr_layout.addWidget(self.local_ocr_group)

        # 远程OCR配置
        self.remote_ocr_group = QGroupBox("远程OCR服务配置")
        remote_ocr_layout = QVBoxLayout()

        remote_config = ocr_config.get("remote_ocr", {})

        self.ocr_server_url_edit = QLineEdit()
        self.ocr_server_url_edit.setText(remote_config.get("server_url", "http://localhost:5000/ocr"))
        self.ocr_server_url_edit.setPlaceholderText("请输入OCR服务地址")

        self.ocr_timeout_edit = QLineEdit()
        self.ocr_timeout_edit.setText(str(remote_config.get("timeout", 30)))
        self.ocr_timeout_edit.setPlaceholderText("超时时间(秒)")

        remote_ocr_layout.addLayout(self.create_horizontal_layout("服务地址:", self.ocr_server_url_edit))
        remote_ocr_layout.addLayout(self.create_horizontal_layout("超时时间(秒):", self.ocr_timeout_edit))

        self.remote_ocr_group.setLayout(remote_ocr_layout)
        ocr_layout.addWidget(self.remote_ocr_group)

        # OCR状态和测试按钮
        self.ocr_status_label = QLabel()
        self.ocr_status_label.setAutoFillBackground(True)
        self.update_ocr_status_label()

        self.ocr_connect_button = QPushButton("测试OCR连接")
        self.ocr_connect_button.setFixedSize(150, 40)
        self.ocr_connect_button.clicked.connect(self.test_ocr_connection)

        ocr_button_layout = QHBoxLayout()
        ocr_button_layout.addStretch()
        ocr_button_layout.addWidget(self.ocr_connect_button)
        ocr_button_layout.addStretch()

        ocr_layout.addWidget(self.ocr_status_label)
        ocr_layout.addLayout(ocr_button_layout)

        # 根据当前模式显示/隐藏配置组
        self.on_ocr_mode_changed()

        ocr_group.setLayout(ocr_layout)
        layout.addWidget(ocr_group)

        # 添加单选按钮组
        radio_group = QGroupBox("选择服务器类型")
        radio_layout = QVBoxLayout()

        self.radio_official = QRadioButton("联系客服申请官方测试服务器")
        self.radio_official.setChecked(False)  # 默认选中
        self.radio_official.toggled.connect(self.on_radio_toggled)

        self.radio_local = QRadioButton("本地服务器")
        self.radio_local.setChecked(True)
        self.radio_local.toggled.connect(self.on_radio_toggled)

        radio_layout.addWidget(self.radio_official)
        radio_layout.addWidget(self.radio_local)

        radio_group.setLayout(radio_layout)
        layout.addWidget(radio_group)

        # 保存配置按钮
        save_button_layout = QHBoxLayout()
        save_button_layout.addStretch()  # 添加伸缩项以居中按钮
        self.save_button = QPushButton("保存配置")
        self.save_button.setFixedSize(150, 40)  # 设置固定大小
        self.save_button.clicked.connect(self.save_config)
        save_button_layout.addWidget(self.save_button)
        save_button_layout.addStretch()  # 添加伸缩项以居中按钮

        layout.addLayout(save_button_layout)

        self.setLayout(layout)

        # 自动选择单选按钮
        self.auto_select_radio_button()

    def create_horizontal_layout(self, label1, edit1, label2=None, edit2=None):
        layout = QHBoxLayout()
        layout.addWidget(QLabel(label1))
        layout.addWidget(edit1)
        if label2 and edit2:
            layout.addWidget(QLabel(label2))
            layout.addWidget(edit2)
        return layout

    def test_server_status(self, status_label, target_host, target_port=None):
        if check_port_available(target_host, target_port):
            palette = status_label.palette()
            palette.setColor(QPalette.WindowText, QColor(0, 128, 0))  # 绿色
            status_label.setPalette(palette)
            status_label.setText(f"{target_host} 可用")
        else:
            palette = status_label.palette()
            palette.setColor(QPalette.WindowText, QColor(255, 0, 0))  # 红色
            status_label.setPalette(palette)
            status_label.setText(f"{target_host} 不可用")
        status_label.update()

    def update_status_label(self, status_label, host, port=None):
        status_label.setText(f"{host}")
        palette = status_label.palette()
        palette.setColor(QPalette.WindowText, QColor(0, 0, 0))
        status_label.setPalette(palette)

    def update_ocr_status_label(self):
        """更新OCR状态标签"""
        self.ocr_status_label.setText("点击测试连接")
        palette = self.ocr_status_label.palette()
        palette.setColor(QPalette.WindowText, QColor(0, 0, 0))
        self.ocr_status_label.setPalette(palette)

    def on_ocr_mode_changed(self):
        """OCR模式切换事件"""
        if self.ocr_mode_local.isChecked():
            self.local_ocr_group.setVisible(True)
            self.remote_ocr_group.setVisible(False)
        else:
            self.local_ocr_group.setVisible(False)
            self.remote_ocr_group.setVisible(True)

    def test_ocr_connection(self):
        """测试OCR连接"""
        try:
            from Tool.ocr_factory import get_ocr_factory

            # 获取当前选择的模式
            current_mode = "local" if self.ocr_mode_local.isChecked() else "remote"

            # 临时保存当前配置进行测试
            temp_config = self.ocr_config_manager.get_ocr_config()
            temp_config['ocr_mode'] = current_mode

            if current_mode == "local":
                # 测试本地百度OCR配置
                app_id = self.baidu_app_id_edit.text().strip()
                api_key = self.baidu_api_key_edit.text().strip()
                secret_key = self.baidu_secret_key_edit.text().strip()

                if not all([app_id, api_key, secret_key]):
                    self.update_ocr_status("error", "请填写完整的百度OCR配置信息")
                    return

                temp_config['baidu_ocr'] = {
                    'app_id': app_id,
                    'api_key': api_key,
                    'secret_key': secret_key
                }
            else:
                # 测试远程OCR配置
                server_url = self.ocr_server_url_edit.text().strip()
                timeout_str = self.ocr_timeout_edit.text().strip()

                if not server_url:
                    self.update_ocr_status("error", "请填写OCR服务地址")
                    return

                try:
                    timeout = int(timeout_str)
                except ValueError:
                    self.update_ocr_status("error", "超时时间必须是数字")
                    return

                temp_config['remote_ocr'] = {
                    'server_url': server_url,
                    'timeout': timeout
                }

            # 临时保存配置并测试
            self.ocr_config_manager.save_ocr_config(temp_config)

            # 重新初始化OCR工厂进行测试
            ocr_factory = get_ocr_factory()
            ocr_factory._initialized = False
            ocr_factory._ocr_engine = None
            ocr_factory._initialize_engine()

            # 测试OCR引擎
            success, message = ocr_factory.test_engine()

            if success:
                self.update_ocr_status("success", f"OCR连接测试成功: {message}")
            else:
                self.update_ocr_status("error", f"OCR连接测试失败: {message}")

        except Exception as e:
            self.update_ocr_status("error", f"测试失败: {str(e)}")

    def update_ocr_status(self, status_type, message):
        """更新OCR状态显示"""
        palette = self.ocr_status_label.palette()
        if status_type == "success":
            palette.setColor(QPalette.WindowText, QColor(0, 128, 0))  # 绿色
        elif status_type == "error":
            palette.setColor(QPalette.WindowText, QColor(255, 0, 0))  # 红色
        else:
            palette.setColor(QPalette.WindowText, QColor(0, 0, 0))  # 黑色

        self.ocr_status_label.setPalette(palette)
        self.ocr_status_label.setText(message)
        self.ocr_status_label.update()

    def on_radio_toggled(self):
        if self.radio_official.isChecked():
            # 设置目标服务器为官方测试服务器
            self.windows_target_host_edit.setText('test.aijiakefu.com')
            self.web_target_host_edit.setText('test.aijiakefu.com')
            self.ocr_server_url_edit.setText('http://************:5000/ocr')
        elif self.radio_local.isChecked():
            # 设置目标服务器为本地服务器
            self.windows_target_host_edit.setText('localhost')
            self.web_target_host_edit.setText('localhost')
            self.ocr_server_url_edit.setText('http://localhost:5000/ocr')

    def save_config(self):
        try:
            # 更新Windows驱动服务器配置
            self.config_manager.update_server_config("WINDOWS_DRIVER_SERVER_IP", self.windows_target_host_edit.text())
            self.config_manager.update_server_config("WINDOWS_DRIVER_SERVER_PORT", int(self.windows_target_port_edit.text()))

            # 更新Web驱动服务器配置
            self.config_manager.update_server_config("WebDriverServer_IP", self.web_target_host_edit.text())
            self.config_manager.update_server_config("WebDriverServer_PORT", int(self.web_target_port_edit.text()))

            # 保存服务器配置
            self.config_manager.save_config()
            self.config_manager.load_config()

            # 保存OCR配置
            ocr_config = self.ocr_config_manager.get_ocr_config()

            # 更新OCR模式
            ocr_config['ocr_mode'] = "local" if self.ocr_mode_local.isChecked() else "remote"

            # 更新百度OCR配置
            ocr_config['baidu_ocr'] = {
                'app_id': self.baidu_app_id_edit.text().strip(),
                'api_key': self.baidu_api_key_edit.text().strip(),
                'secret_key': self.baidu_secret_key_edit.text().strip()
            }

            # 更新远程OCR配置
            ocr_config['remote_ocr'] = {
                'server_url': self.ocr_server_url_edit.text().strip(),
                'timeout': int(self.ocr_timeout_edit.text().strip())
            }

            if self.ocr_config_manager.save_ocr_config(ocr_config):
                QMessageBox.information(self, "成功", "服务器配置已成功保存，请重启客户端生效")
            else:
                QMessageBox.critical(self, "错误", "保存OCR配置失败")

        except ValueError:
            QMessageBox.critical(self, "错误", "端口号和超时时间必须是数字")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存服务器配置文件失败: {e}")

    def auto_select_radio_button(self):
        windows_host = self.windows_target_host_edit.text()
        web_host = self.web_target_host_edit.text()
        ocr_url = self.ocr_server_url_edit.text()

        if (windows_host in ['localhost', '127.0.0.1'] and
            web_host in ['localhost', '127.0.0.1'] and
            'localhost' in ocr_url):
            self.radio_local.setChecked(True)
        else:
            self.radio_official.setChecked(True)

def main():
    app = QApplication([])
    window = ProxyConfigPage()
    window.show()
    app.exec_()

if __name__ == "__main__":
    main()