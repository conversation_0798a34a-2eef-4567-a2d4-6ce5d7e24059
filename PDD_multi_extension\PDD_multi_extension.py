#拼多多多开扩展最多100个
import socket
import sys
import json
import threading
from typing import Dict, Any, List

import yaml
from PyQt5.QtWidgets import (
    QApplication, QDialog, QVBoxLayout, QHBoxLayout,
    QCheckBox, QLabel, QLineEdit, QPushButton, QWidget, QSizePolicy, QScrollArea
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon
from PDDmultiwebdriver import WebDriver  # 拼多多适用
# from ClietServer import ServerAgency

class ServerAgency:
    def __init__(self, target_host=None, target_port=None, proxy_host=None, proxy_port=None):

        # Web驱动服务器配置
        self.TARGET_HOST = target_host
        self.TARGET_PORT = target_port

        # Windows驱动服务器配置
        self.PROXY_HOST = proxy_host
        self.PROXY_PORT = proxy_port

        if self.PROXY_HOST and self.PROXY_PORT:
            # 创建代理服务器的监听套接字
            self.proxy_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.proxy_socket.bind((self.PROXY_HOST, self.PROXY_PORT))
            self.proxy_socket.listen(5)
            print(f"[*] Listening on {self.PROXY_HOST}:{self.PROXY_PORT}")

    def handle_client(self, client_socket):
        # 创建连接到目标服务器的套接字
        self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.server_socket.connect((self.TARGET_HOST, self.TARGET_PORT))

        while True:
            # 接收来自目标服务器的响应数据
            response = self.server_socket.recv(4096)

            print("[*] 目标服务器连接成功！Received response from server:")
            print(response.decode())

            # 将响应数据转发给客户端
            client_socket.sendall(response)

            # 接收来自客户端的请求数据
            request = client_socket.recv(4096*1024)
            print(request.decode())

            self.server_socket.sendall(request)

    def ClietServerStart(self):
        while True:
            # 接受来自客户端的连接以验证服务器可用性
            self.client_socket, self.addr = self.proxy_socket.accept()
            print(f"[*] 服务器连接成功！Accepted connection from {self.addr}")

            # 创建一个新的线程来处理客户端请求
            client_handler = threading.Thread(target=self.handle_client, args=(self.client_socket,))
            client_handler.start()

    def ServerClose(self):
        try:
            self.server_socket.close()
            self.client_socket.close()
        except Exception as e:
            pass

    def ClietServerMain(self):
        # 创建一个新的线程来处理客户端请求
        client_main = threading.Thread(target=self.ClietServerStart, args=())
        client_main.daemon = True
        client_main.start()

class PDDMultiInstanceDialog(QDialog):
    proxy_configs = {i: (f'127.0.3.{i}', 8670 + i) for i in range(1, 101)}

    def __init__(self, shop_names, web_drivers, proxy_servers, parent=None):
        super().__init__(parent)
        self.setWindowTitle('拼多多多开扩展')

        self.setWindowIcon(QIcon("./Static/ProgramImage/logo.ico"))
        self.setFixedSize(700, 500)

        self.web_drivers = web_drivers
        self.proxy_servers = proxy_servers
        self.base_debug_port = 8971
        # merged_config = self.merge_configs('config.json')
        # print("初始中获取的配置是：", merged_config)
        self.main_layout = QVBoxLayout(self)
        self.checkboxes = []
        self.shop_name_edits = []

        self.setup_ui()
        self.load_initial_state()

    def setup_ui(self):
        # 使用 QScrollArea 以支持滚动布局
        scroll_area = QScrollArea(self)
        scroll_area.setWidgetResizable(True)
        self.scroll_widget = QWidget()
        self.scroll_layout = QVBoxLayout(self.scroll_widget)

        # 初始创建 30 个复选框和文本框
        for i in range(1, 101):
            self.add_checkbox_with_textbox(i)

        scroll_area.setWidget(self.scroll_widget)
        self.main_layout.addWidget(scroll_area)

        # 按钮布局
        button_layout = QHBoxLayout()
        self.add_button = QPushButton('新增多开', self)
        self.add_button.setEnabled(False)
        self.add_button.clicked.connect(self.add_new_instance)
        button_layout.addWidget(self.add_button)
        self.main_layout.addLayout(button_layout)

    def load_initial_state(self):
        merged_config = self.merge_configs('./Tool/serverConfiguration.yaml',"./Tool/config.yaml", './Tool/config.json')
        print("初始中获取的配置是：", merged_config)

        # 将列表转换为以 'name' 为键的字典，便于查询
        config_dict =merged_config



        print("config_dict is",config_dict)

        # 载入店铺名称到文本框
        for i, edit in enumerate(self.shop_name_edits, start=1):
            shop_key = f'拼多多多开{i}'
            print("shop_key",shop_key)
            shop_name = config_dict.get(shop_key, {}).get('Shop Name', '')
            print("shop_name",shop_name)
            edit.setText(f'店铺名称：{shop_name}')

    def merge_configs(self, server_config_path: str, config_yaml_path: str, config_json_path: str,
                      duokaidict: Dict[str, Any] = {}) -> Dict[str, Any]:
        def load_yaml(file_path: str) -> Dict[str, Any]:
            try:
                with open(file_path, 'r', encoding='utf-8') as file:
                    content = yaml.safe_load(file)
                    if isinstance(content, list):
                        content = {str(index): value for index, value in enumerate(content)}
                    elif not isinstance(content, dict):
                        raise TypeError(f"{file_path} content is neither a dictionary nor a list")
                    return content
            except FileNotFoundError:
                print(f"File {file_path} not found.")
                return {}
            except yaml.YAMLError as e:
                print(f"Error loading YAML file {file_path}: {e}")
                return {}

        def load_json(file_path: str) -> Dict[str, Any]:
            config_dict = {}
            try:
                with open(file_path, 'r', encoding='utf-8') as file:
                    configs = json.load(file)
                    if not configs:
                        print("Configuration file is empty.")
                        return config_dict

                    for config in configs:
                        config_name = config.get('name', 'N/A')
                        config_dict[config_name] = {
                            "Shop Name": config.get('shop_name', 'N/A'),
                            "API Base": config.get('api_base', 'N/A'),
                            "Key": config.get('key', 'N/A'),
                            "Customer Service Name": config.get('customer_service_name', 'N/A')
                        }
                    return config_dict
            except FileNotFoundError:
                print(f"File {file_path} not found.")
                return config_dict
            except json.JSONDecodeError as e:
                print(f"Error parsing JSON file {file_path}: {e}")
                return config_dict

        server_config = load_yaml(server_config_path)
        config_yaml = load_yaml(config_yaml_path)
        config_json = load_json(config_json_path)

        merged_config = {**server_config, **config_yaml, **config_json, **duokaidict}

        return merged_config

    def add_checkbox_with_textbox(self, index):
        checkbox_hbox = QHBoxLayout()

        checkbox = QCheckBox(f'拼多多多开{index}', self)
        checkbox.stateChanged.connect(self.toggle_instance)
        self.checkboxes.append(checkbox)
        checkbox_hbox.addWidget(checkbox)

        shop_name_edit = QLineEdit(self)
        shop_name_edit.setFixedSize(180, 26)
        shop_name_edit.setReadOnly(True)
        self.shop_name_edits.append(shop_name_edit)
        checkbox_hbox.addWidget(shop_name_edit)

        self.scroll_layout.addLayout(checkbox_hbox)

    def add_new_instance(self):
        # 动态添加新的复选框和文本框
        new_index = len(self.checkboxes) + 1
        self.add_checkbox_with_textbox(new_index)

    def toggle_instance(self, state):
        checkbox = self.sender()
        instance_index = self.checkboxes.index(checkbox) + 1
        print("instance_index是",instance_index)
        if state == Qt.Checked:
            print(106)
            self.start_instance(instance_index)
            print(107)
        else:
            self.stop_instance(instance_index)

    def start_instance(self, instance_index):
        duokaidict = {"duokaikey": f'拼多多多开{instance_index}'}
        print(110)

        merged_config = self.merge_configs('./Tool/serverConfiguration.yaml', './Tool/config.yaml', './Tool/config.json',
                                           duokaidict)
        print("获取的配置是：", merged_config)

        webdriver_ip = merged_config['server']['WebDriverServer_IP']
        print("获取到的拼多多服务器ip是：", webdriver_ip)

        target_host_PDD = webdriver_ip
        proxy_host, proxy_port = self.proxy_configs.get(instance_index, ('127.0.0.1', 9994))
        debug_port = self.base_debug_port + instance_index - 1
        user_data_dir = f"./UserData/PDD/{instance_index}"

        parameter = merged_config
        # parameter = self.merged_config


        print("parameter是",parameter)
        if instance_index not in self.web_drivers:
            proxy_server = ServerAgency(target_host_PDD, 9994, proxy_host, proxy_port)
            self.proxy_servers[instance_index] = proxy_server

            web_driver = WebDriver(proxy_host, proxy_port, parameter, debug_port, user_data_dir)
            self.web_drivers[instance_index] = web_driver

            try:
                web_driver.start()
                proxy_server.ClietServerMain()
            except Exception as e:
                print(f"启动实例 {instance_index} 时出错：{e}")
        else:
            # 重新创建并启动WebDriver
            web_driver = WebDriver(proxy_host, proxy_port, parameter, debug_port, user_data_dir)
            self.web_drivers[instance_index] = web_driver

            try:
                web_driver.start()
                self.proxy_servers[instance_index].ClietServerMain()
            except Exception as e:
                print(f"启动实例 {instance_index} 时出错：{e}")
    def stop_instance(self, instance_index):
        try:
            self.web_drivers[instance_index].stop()
            self.proxy_servers[instance_index].ServerClose()
        except Exception as e:
            print(f"停止实例 {instance_index} 时发生异常: {e}")


if __name__ == '__main__':
    app = QApplication(sys.argv)
    dialog = PDDMultiInstanceDialog({}, {}, {})
    dialog.show()
    sys.exit(app.exec_())
