import requests

# 本地软件版本号
LOCAL_VERSION = "1.0.8"

# 获取最新的 GitHub release 版本号和 RAR 文件下载链接
def get_latest_github_release_version(repo_url):
    api_url = f"https://api.github.com/repos/{repo_url}/releases/latest"
    response = requests.get(api_url)
    if response.status_code == 200:
        latest_release = response.json()
        tag_name = latest_release.get('tag_name', 'No tag name found')
        name = latest_release.get('name', 'No name found')
        latest_version = tag_name if tag_name.startswith('v') else name
        assets = latest_release['assets']
        rar_asset = next((asset for asset in assets if asset['name'].endswith('.rar')), None)
        if rar_asset:
            rar_download_url = rar_asset['browser_download_url']
            return latest_version, rar_download_url
        else:
            raise Exception("No RAR file found in the latest release.")
    else:
        raise Exception("Failed to fetch latest release version from GitHub.")

# 比较版本号
def compare_versions(local_version, latest_version):
    return local_version != latest_version

# 下载 RAR 文件
def download_rar(rar_download_url, download_path):
    response = requests.get(rar_download_url, stream=True)
    with open(download_path, 'wb') as file:
        for chunk in response.iter_content(chunk_size=1024):
            if chunk:
                file.write(chunk)

# 检查更新并提供下载选项
def check_for_updates():
    repo_url = "AIjiaKeFu/AI-Customer-Service"
    download_path = "latest_version.rar"

    try:
        latest_version, rar_download_url = get_latest_github_release_version(repo_url)

        if compare_versions(LOCAL_VERSION, latest_version):
            print(f"New version available: {latest_version}")
            user_input = input("Do you want to download the new version? (yes/no): ").strip().lower()
            if user_input == "yes":
                download_rar(rar_download_url, download_path)
                print("RAR file downloaded successfully.")
            else:
                print("Download cancelled by user.")
        else:
            print("Your software is up to date.")

    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    check_for_updates()