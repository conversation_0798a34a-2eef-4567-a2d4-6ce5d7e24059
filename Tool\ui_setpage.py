import os
from PyQt5 import QtWidgets, QtCore, QtGui
from PyQt5.QtGui import <PERSON>Font
from PyQt5.QtWidgets import QLabel, QLineEdit, QComboBox, QPushButton, QFrame, QGroupBox, QVBoxLayout, QHBoxLayout

def setpage(self):#设置区
    # 微信设置
    self.page_11 = QtWidgets.QWidget()
    self.page_11.setObjectName("page_11")

    font = QFont()
    font.setPointSize(12)

    # 用户名输入区
    username_group = QGroupBox("抖音用户名设置", self.page_11)
    username_layout = QHBoxLayout()

    self.username_labelsrk = QLabel("抖音用户名:", self.page_11)
    self.username_labelsrk.setFont(font)
    username_layout.addWidget(self.username_labelsrk)

    self.username_edit = QLineEdit(self.page_11)
    self.username_edit.setFont(font)
    self.username_edit.setPlaceholderText("输入昵称才查看回复@昵称的群消息")
    self.username_edit.setFixedWidth(270)   #文本框宽度
    self.username_edit.setFixedHeight(25)   # 统一文本框高度
    username_layout.addWidget(self.username_edit)

    username_group.setLayout(username_layout)
    username_group.setGeometry(QtCore.QRect(20, 110, 400, 50))  # 将宽度改为 400

    # 全局API设置区
    global_group = QGroupBox("全局AI设置", self.page_11)
    global_layout = QVBoxLayout()

    global_api_base_layout = QHBoxLayout()
    self.global_api_base_label = QLabel("API Base:", self.page_11)
    self.global_api_base_label.setFont(font)
    self.global_api_base_label.hide()  # 隐藏标签
    global_api_base_layout.addWidget(self.global_api_base_label)

    self.global_api_base_edit = QLineEdit(self.page_11)
    self.global_api_base_edit.setFont(font)
    self.global_api_base_edit.setFixedHeight(25)
    self.global_api_base_edit.setText("http://api.aijiakefu.com/v1")  # 设置固定值
    self.global_api_base_edit.hide()  # 隐藏输入框
    global_api_base_layout.addWidget(self.global_api_base_edit)

    global_key_layout = QHBoxLayout()
    self.global_key_label = QLabel("Key:", self.page_11)
    self.global_key_label.setFont(font)
    global_key_layout.addWidget(self.global_key_label)

    self.global_key_edit = QLineEdit(self.page_11)
    self.global_key_edit.setFont(font)
    self.global_key_edit.setFixedHeight(25)   # 统一文本框高度
    global_key_layout.addWidget(self.global_key_edit)

    global_layout.addLayout(global_api_base_layout)
    global_layout.addLayout(global_key_layout)

    global_group.setLayout(global_layout)
    global_group.setGeometry(QtCore.QRect(20, 170, 400, 100))  # 将宽度改为 400

    # 单独AI设置区
    individual_group = QGroupBox("单独AI设置", self.page_11)
    individual_layout = QVBoxLayout()

    ai_selection_layout = QHBoxLayout()
    self.ai_selection_label = QLabel("平台AI设置:", self.page_11)
    self.ai_selection_label.setFont(font)
    ai_selection_layout.addWidget(self.ai_selection_label)

    self.ai_selection_combo = QComboBox(self.page_11)
    self.ai_selection_combo.addItems(["企业微信", "抖音", "抖店"])
    self.ai_selection_combo.setFont(font)
    ai_selection_layout.addWidget(self.ai_selection_combo)

    individual_api_base_layout = QHBoxLayout()
    self.individual_api_base_label = QLabel("API Base:", self.page_11)
    self.individual_api_base_label.setFont(font)
    self.individual_api_base_label.hide()  # 隐藏标签
    individual_api_base_layout.addWidget(self.individual_api_base_label)

    self.individual_api_base_edit = QLineEdit(self.page_11)
    self.individual_api_base_edit.setFont(font)
    self.individual_api_base_edit.setFixedHeight(25)
    self.individual_api_base_edit.setText("http://api.aijiakefu.com/v1")  # 设置固定值
    self.individual_api_base_edit.hide()  # 隐藏输入框
    individual_api_base_layout.addWidget(self.individual_api_base_edit)

    individual_key_layout = QHBoxLayout()
    self.individual_key_label = QLabel("Key:", self.page_11)
    self.individual_key_label.setFont(font)
    individual_key_layout.addWidget(self.individual_key_label)

    self.individual_key_edit = QLineEdit(self.page_11)
    self.individual_key_edit.setFont(font)
    self.individual_key_edit.setFixedHeight(25)   # 统一文本框高度
    individual_key_layout.addWidget(self.individual_key_edit)

    individual_layout.addLayout(ai_selection_layout)
    individual_layout.addLayout(individual_api_base_layout)
    individual_layout.addLayout(individual_key_layout)

    individual_group.setLayout(individual_layout)
    individual_group.setGeometry(QtCore.QRect(20, 280, 400, 150))  # 将宽度改为 400

    # 分隔线
    separator = QFrame(self.page_11)
    separator.setFrameShape(QFrame.HLine)
    separator.setFrameShadow(QFrame.Sunken)
    separator.setGeometry(QtCore.QRect(20, 440, 400, 1))  # 将宽度改为 400

    # 保存按钮
    self.pushButton_13 = QtWidgets.QPushButton(self.page_11)
    self.pushButton_13.setGeometry(QtCore.QRect(190, 530, 111, 31))  # 将 y 值改为 490
    self.pushButton_13.setStyleSheet("""
        QPushButton {
            background-color: qlineargradient(
                spread:pad, x1:0, y1:0, x2:1, y2:1, 
                stop:0 rgba(76, 175, 80, 255), 
                stop:1 rgba(56, 142, 60, 255)
            );  /* 使用绿色渐变背景 */
            color: white;  /* 白色字体 */
            font-size: 11pt;  /* 字体稍微小一点 */
            font-weight: bold;  /* 字体加粗 */
            border-radius: 10px;  /* 圆角边框 */
            border: 2px solid #388e3c;  /* 绿色边框 */
            padding: 5px;  /* 增加内边距 */
        }
        QPushButton:hover {
            background-color: qlineargradient(
                spread:pad, x1:0, y1:0, x2:1, y2:1, 
                stop:0 rgba(56, 142, 60, 255), 
                stop:1 rgba(76, 175, 80, 255)
            );  /* 鼠标悬停时反转渐变色 */
        }
        QPushButton:pressed {
            background-color: #2e7d32;  /* 按下时变为深绿色 */
        }
    """)
    self.pushButton_13.setObjectName("pushButton_13")

    # 配置服务器的按钮
    self.config_button = QPushButton("配置服务器", self.page_11)
    self.config_button.clicked.connect(self.open_proxy_config_page)
    self.config_button.setGeometry(QtCore.QRect(135, 580, 111, 40))  # 调整位置使其居中
    self.config_button.setStyleSheet("""
        QPushButton {
            background-color: qlineargradient(
                spread:pad, x1:0, y1:0, x2:1, y2:1, 
                stop:0 rgba(85, 170, 255, 255), 
                stop:1 rgba(0, 85, 255, 255)
            );  /* 使用渐变色背景 */
            color: white;  /* 白色字体 */
            font-size: 11pt;  /* 字体稍微小一点 */
            font-weight: bold;  /* 字体加粗 */
            border-radius: 10px;  /* 圆角边框 */
            border: 2px solid #0055ff;  /* 蓝色边框 */
            padding: 5px;  /* 增加内边距 */
        }
        QPushButton:hover {
            background-color: qlineargradient(
                spread:pad, x1:0, y1:0, x2:1, y2:1, 
                stop:0 rgba(0, 85, 255, 255), 
                stop:1 rgba(85, 170, 255, 255)
            );  /* 鼠标悬停时反转渐变色 */
        }
        QPushButton:pressed {
            background-color: #0044cc;  /* 按下时变为深蓝色 */
        }
    """)
    self.config_button.setObjectName("config_button")

    # 多开配置设置按钮
    self.multi_config_button = QPushButton("多开配置设置", self.page_11)
    self.multi_config_button.clicked.connect(self.open_multi_instance_control_window)
    self.multi_config_button.setGeometry(QtCore.QRect(276, 580, 111, 40))  # 调整位置使其居中
    self.multi_config_button.setStyleSheet("""
        QPushButton {
            background-color: qlineargradient(
                spread:pad, x1:0, y1:0, x2:1, y2:1, 
                stop:0 rgba(85, 170, 255, 255), 
                stop:1 rgba(0, 85, 255, 255)
            );  /* 使用渐变色背景 */
            color: white;  /* 白色字体 */
            font-size: 11pt;  /* 字体稍微小一点 */
            font-weight: bold;  /* 字体加粗 */
            border-radius: 10px;  /* 圆角边框 */
            border: 2px solid #0055ff;  /* 蓝色边框 */
            padding: 5px;  /* 增加内边距 */
        }
        QPushButton:hover {
            background-color: qlineargradient(
                spread:pad, x1:0, y1:0, x2:1, y2:1, 
                stop:0 rgba(0, 85, 255, 255), 
                stop:1 rgba(85, 170, 255, 255)
            );  /* 鼠标悬停时反转渐变色 */
        }
        QPushButton:pressed {
            background-color: #0044cc;  /* 按下时变为深蓝色 */
        }
    """)
    self.multi_config_button.setObjectName("multi_config_button")

    # 返回主页按钮
    self.pushButton_zhuye2 = QtWidgets.QPushButton(self.page_11)
    self.pushButton_zhuye2.setGeometry(QtCore.QRect(460, 50, 35, 35))  # 将 y 值改为 50
    self.pushButton_zhuye2.setStyleSheet("""
        QPushButton {
            background-color: rgba(0, 0, 0, 0);
        }
    """)
    icon5 = QtGui.QIcon()
    icon5.addPixmap(QtGui.QPixmap(f"{os.getcwd()}/Static/ProgramImage/home.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
    self.pushButton_zhuye2.setText("")
    self.pushButton_zhuye2.setIcon(icon5)
    self.pushButton_zhuye2.setIconSize(QtCore.QSize(35, 35))
    self.pushButton_zhuye2.setObjectName("pushButton_zhuye2")

    # 抖店转接人工客服文本框
    self.manualCustomerDouyin = QtWidgets.QLineEdit(self.page_11)
    self.manualCustomerDouyin.setGeometry(QtCore.QRect(150, 450, 180, 30))  # 将 y 值改为 450
    self.manualCustomerDouyin.setPlaceholderText("抖店转接人工客服")
    self.manualCustomerDouyin.setObjectName("manualCustomerDouyin")

    # 抖店转接人工客服标签
    self.label_douyin_customer = QLabel("抖店人工客服:", self.page_11)
    self.label_douyin_customer.setFont(font)
    self.label_douyin_customer.setGeometry(QtCore.QRect(20, 450, 120, 30))  # 将 y 值改为 450
    self.label_douyin_customer.setObjectName("label_douyin_customer")

    # 人工客服微信
    self.manualCustomerWeChat = QtWidgets.QLineEdit(self.page_11)
    self.manualCustomerWeChat.setGeometry(QtCore.QRect(150, 490, 180, 30))  # 将 y 值改为 490
    self.manualCustomerWeChat.setPlaceholderText("昵称")
    self.manualCustomerWeChat.setObjectName("manualCustomerWeChat")

    # 人工客服微信标签
    self.label_10 = QtWidgets.QLabel(self.page_11)
    self.label_10.setGeometry(QtCore.QRect(20, 490, 120, 30))  # 将 y 值改为 490
    self.label_10.setObjectName("label_10")

    # 设置标签
    self.label_12 = QtWidgets.QLabel(self.page_11)
    self.label_12.setGeometry(QtCore.QRect(220, 70, 101, 41))  # 将 y 值改为 70
    self.label_12.setObjectName("label_12")

    # 其他标签
    self.label_13 = QtWidgets.QLabel(self.page_11)
    self.label_13.setGeometry(QtCore.QRect(630, 610, 81, 21))  # 将 y 值改为 610
    self.label_13.setObjectName("label_13")

    self.label_15 = QtWidgets.QLabel(self.page_11)
    self.label_15.setGeometry(QtCore.QRect(310, 485, 51, 41))  # 将 y 值改为 485
    self.label_15.setObjectName("label_15")

    self.stackedWidget.addWidget(self.page_11)
