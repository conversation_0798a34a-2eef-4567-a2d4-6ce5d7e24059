# main.py
import os
import sys
import time
from PyQt5.QtCore import QThread, QTimer
from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QMessageBox
from PyQt5.QtGui import QIcon
from Tool.ui import Ui_MainWindow as AiJiaMainWindows
from Tool.ClietServer import ServerAgency, check_port_available  # 确保导入 check_port_available
from Tool.DriverStart import Driver
from Tool.DriverStartdouyin import DriveDouyin
from Tool.login import LoginWindow
from Tool.auth_manager import AuthManager
from Tool.config_manager import ConfigManager
from Tool.multi_instance_control_window import MultiInstanceControlWindow
from Tool import browser_utils
from Tool.DriverStartdoudian import DriveDoudian
from Tool.DriverStartJD import DriveJD
from Tool.baidu_ocr_client import get_ocr_client
from PyQt5.QtCore import QThread, pyqtSignal


# 企业微信线程
class WindowsDriver(QThread, Driver):
    def __init__(self, ip, port, parameter, parent=None):
        QThread.__init__(self, parent)
        Driver.__init__(self)
        self.ip = ip
        self.port = port
        self.parameter = parameter

    def run(self):
        try:
            print("ok1run")
            print(
                f"[{time.strftime('%H:%M:%S')}] WindowsDriverStart 启动，地址：{self.ip}:{self.port}，参数：{self.parameter}")
            self.WindowsDriverStart(self.ip, self.port, self.parameter)
            print(f"[{time.strftime('%H:%M:%S')}] WindowsDriverStart 完成")
        except Exception as e:
            print(f"运行中异常: {e}")


# 抖音线程
class WebDriver(QThread, DriveDouyin):
    def __init__(self, ip, port, parameter, parent=None):
        super().__init__(parent)
        self.ip = ip
        self.port = port
        self.parameter = parameter

    def run(self):
        try:
            print(f"[{time.strftime('%H:%M:%S')}] start_server 启动，地址：{self.ip}:{self.port}")
            self.start_server(self.ip, self.port, self.parameter)
            print(f"[{time.strftime('%H:%M:%S')}] start_server 完成")
        except Exception as e:
            print(f"运行中异常: {e}")


class DoudianWebDriver(QThread, DriveDoudian):
    def __init__(self, ip, port, parameter, debug_port, user_data_dir, parent=None):
        super().__init__(parent)
        self.ip = ip
        self.port = port
        self.parameter = parameter
        self.debug_port = debug_port
        self.user_data_dir = user_data_dir

    def run(self):
        self.running = True
        try:
            print("进入run")
            self.start_server(self.ip, self.port, self.parameter, self.debug_port, self.user_data_dir)
            print("进出run")
        except Exception as e:
            print(f"运行中异常: {e}")
class JDWebDriver(QThread, DriveJD):
    def __init__(self, ip, port, parameter, debug_port, user_data_dir, parent=None):
        super().__init__(parent)
        self.ip = ip
        self.port = port
        self.parameter = parameter
        self.debug_port = debug_port
        self.user_data_dir = user_data_dir

    def run(self):
        self.running = True
        try:
            print("进入run")
            self.start_server(self.ip, self.port, self.parameter, self.debug_port, self.user_data_dir)
            print("进出run")
        except Exception as e:
            print(f"运行中异常: {e}")

class AiJakeFuClient(QMainWindow, AiJiaMainWindows):
    def __init__(self):
        super(AiJakeFuClient, self).__init__()
        self.setWindowIcon(QIcon("./Static/ProgramImage/logo.ico"))
        self.setupUi(self)
        # 多开抖店部分参数
        self.state = [False] * 8
        self.shop_names = [""] * 8
        self.web_drivers = {}
        self.proxy_servers = {}

        # 多开京东部分参数
        self.stateJD = [False] * 8
        self.shop_namesJD = [""] * 8
        self.web_driversJD = {}
        self.proxy_serversJD = {}

        # 多开拼多多部分参数
        self.statePDD = [False] * 8
        self.shop_namesPDD = [""] * 8
        self.web_driversPDD = {}
        self.proxy_serversPDD = {}

        # 多开快手部分参数
        self.stateKS = [False] * 8
        self.shop_namesKS = [""] * 8
        self.web_driversKS = {}
        self.proxy_serversKS = {}

        # 多开芝麻部分参数
        self.statezhima = [False] * 8
        self.shop_nameszhima = [""] * 8
        self.web_driverszhima = {}
        self.proxy_serverszhima = {}

        # 多开抖音部分参数
        self.statedouyin = [False] * 8
        self.shop_namesdouyin = [""] * 8
        self.web_driversdouyin = {}
        self.proxy_serversdouyin = {}


        self.is_running = False  # 主按钮按下标志位

        # 复选框部分
        # self.prev_checkBox_state = True
        # self.prev_checkBox_douyin_state = True
        # self.prev_checkBox_doudian_state = True
        # self.prev_checkBox_qianniu_state = True
        # self.prev_checkBox_JD_state = True #  京东
        # self.prev_checkBox_PDD_state = True #  拼多多



        self.config_manager = ConfigManager()  # 配置管理类
        self.duokaiconfig_manager = MultiInstanceControlWindow()
        self.api_config = self.config_manager.api_config

        # print("self.config_manager.api_config:", self.api_config)
        server_config = self.config_manager.get_server_config()

        # 企业微信参数
        print("144 服务器配置:", server_config)
        self.target_host = server_config.get("WINDOWS_DRIVER_SERVER_IP", "localhost")
        self.target_port = server_config.get("WINDOWS_DRIVER_SERVER_PORT", 9999)
        self.proxy_host = '127.0.0.1'
        self.proxy_port = 8888
        self.ClientLocalServer = ServerAgency(self.target_host, self.target_port, self.proxy_host, self.proxy_port)

        # 抖音参数
        self.target_host_douyin = server_config.get("WebDriverServer_IP", "localhost")
        self.target_port_douyin = server_config.get("WebDriverServer_PORT", 9998)  # 修改为 9998
        self.proxy_host_douyin = '*********'
        self.proxy_port_douyin = 8881
        self.douyin_debug_port = 8989
        self.user_data_dir_douyin = f"./UserData/DY/0"
        self.ClientLocalServerDouyin = ServerAgency(self.target_host_douyin, self.target_port_douyin,
                                                    self.proxy_host_douyin, self.proxy_port_douyin)
        # 抖店参数
        # self.target_host_doudian = '*************'
        self.target_host_doudian = self.target_host_douyin
        self.target_port_doudian = 9997  # 抖音真实端口
        self.proxy_host_doudian = '***********'
        self.proxy_port_doudian = 8882
        self.Doudian_debug_port = 8990
        self.user_data_dir = f"./UserData/doudian/0"
        self.ClientLocalServerDoudian = ServerAgency(self.target_host_doudian, self.target_port_doudian,
                                                     self.proxy_host_doudian, self.proxy_port_doudian)

        # 千牛参数
        self.qianniu_target_host = server_config.get("WINDOWS_DRIVER_SERVER_IP", "localhost")
        self.qianniu_target_port = 29990
        self.qianniu_proxy_host = '*********'
        self.qianniu_proxy_port = 8883
        self.qianniu_ClientLocalServer = ServerAgency(self.qianniu_target_host, self.qianniu_target_port, self.qianniu_proxy_host, self.qianniu_proxy_port)

        # 京东参数
        # self.target_host_doudian = '*************'
        self.target_host_JD = self.target_host_douyin
        self.target_port_JD = 19995  # 京东真实端口
        self.proxy_host_JD = '***********'
        self.proxy_port_JD = 8884
        self.JD_debug_port = 8799
        self.user_data_dir_JD = f"./UserData/JD/0"
        self.ClientLocalServerJD = ServerAgency(self.target_host_JD, self.target_port_JD,
                                                     self.proxy_host_JD, self.proxy_port_JD)

        # 拼多多参数
        # self.target_host_doudian = '*************'
        self.target_host_PDD = self.target_host_douyin
        self.target_port_PDD = 9994  # 拼多多真实端口
        self.proxy_host_PDD = '***********'
        self.proxy_port_PDD = 8789
        self.PDD_debug_port = 8599
        self.user_data_dir_PDD = f"./UserData/PDD/0"
        self.ClientLocalServerPDD = ServerAgency(self.target_host_PDD, self.target_port_PDD,
                                                self.proxy_host_PDD, self.proxy_port_PDD)

        # 快手参数
        # self.target_host_doudian = '*************'
        self.target_host_KS = self.target_host_douyin
        self.target_port_KS = 9993  # 快手真实端口
        self.proxy_host_KS = '***********'
        self.proxy_port_KS = 8790
        self.KS_debug_port = 8598
        self.user_data_dir_KS = f"./UserData/KS/0"
        self.ClientLocalServerKS = ServerAgency(self.target_host_KS, self.target_port_KS,
                                                 self.proxy_host_KS, self.proxy_port_KS)

        # 芝麻客服参数
        self.target_host_zhima = self.target_host_douyin
        self.target_port_zhima = 9992  # 芝麻小客服真实端口
        self.proxy_host_zhima = '***********'
        self.proxy_port_zhima = 8791
        self.zhima_debug_port = 8597
        self.user_data_dir_zhima = f"./UserData/zhima/0"
        self.ClientLocalServerzhima = ServerAgency(self.target_host_zhima, self.target_port_zhima,
                                                self.proxy_host_zhima, self.proxy_port_zhima)
















        # 初始化百度OCR客户端
        self.ocr_client = get_ocr_client()
        print("百度OCR客户端已初始化")

        self.pushButton_yemian1.clicked.connect(self.toggle)  #开始按钮

        # self.checkBox.stateChanged.connect(self.handle_checkbox_change)
        # self.checkBox_douyin.stateChanged.connect(self.handle_checkbox_change)
        # self.checkBox_doudian.stateChanged.connect(self.handle_checkbox_change)
        # self.checkBox_qianniu.stateChanged.connect(self.handle_checkbox_change) # 千牛复选框
        # self.checkBox_jingmai.stateChanged.connect(self.handle_checkbox_change) # 京东复选框
        # self.checkBox_pdd.stateChanged.connect(self.handle_checkbox_change) # 京东复选框

        self.checkBox.stateChanged.connect(
            lambda: self.on_checkbox_state_changed(self.checkBox, self.start_wechat, self.stop_wechat))
        self.checkBox_qianniu.stateChanged.connect(
            lambda: self.on_checkbox_state_changed(self.checkBox_qianniu, self.start_qianniu, self.stop_qianniu))
        self.checkBox_douyin.stateChanged.connect(
            lambda: self.on_checkbox_state_changed(self.checkBox_douyin, self.start_douyin, self.stop_douyin))
        self.checkBox_doudian.stateChanged.connect(
            lambda: self.on_checkbox_state_changed(self.checkBox_doudian, self.start_doudian, self.stop_doudian))
        self.checkBox_jingmai.stateChanged.connect(
            lambda: self.on_checkbox_state_changed(self.checkBox_jingmai, self.start_JD, self.stop_JD))
        self.checkBox_pdd.stateChanged.connect(
            lambda: self.on_checkbox_state_changed(self.checkBox_pdd, self.start_PDD, self.stop_PDD))
        self.checkBox_kuaishou.stateChanged.connect(
            lambda: self.on_checkbox_state_changed(self.checkBox_kuaishou, self.start_KS, self.stop_KS))
        self.checkBox_zhima.stateChanged.connect(
            lambda: self.on_checkbox_state_changed(self.checkBox_zhima, self.start_zhima, self.stop_zhima))


        self.ai_selection_combo.currentIndexChanged.connect(self.load_individual_settings)  #配置服务器页面按钮
        self.pushButton_13.clicked.connect(self.save_config)  #设置页面保存按钮

        self.username_label = QLabel(self)
        self.username_label.setStyleSheet("font-size: 14px; color: blue;")
        self.username_label.move(self.width() - 100, 10)
        self.username_label.hide()

        self.role_label = QLabel(self)
        self.role_label.setStyleSheet("font-size: 14px; color: green;")
        self.role_label.move(self.width() - 100, 30)
        self.role_label.hide()

        self.textBrowser.append(
            f"<div style='color:orange;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 正在初始化<b></div>")
        self.textBrowser.append(
            f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 初始化完成<b></div>")
        self.textBrowser.append(
            f"<div style='color:orange;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 正在获取爱嘉客服服务授权<b></div>")
        self.textBrowser.append(
            f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 获取爱嘉客服服务授权成功<b></div>")
        # print(666)
        print("259 self.config_manager.api_config:", self.config_manager.api_config)

        self.load_config()

        # 初始化 parameter 属性
        self.parameter = {}  # 或者根据需要初始化为其他值

    def load_config(self):
        self.config_manager.load_config()
        self.username_edit.setText(self.config_manager.api_config.get("username", ""))
        # 设置固定的API Base
        self.global_api_base_edit.setText("http://ip.aijiakefu.com/v1")
        self.global_key_edit.setText(self.config_manager.api_config["global"].get("key", ""))
        self.manualCustomerWeChat.setText(self.config_manager.api_config.get("人工客服", ""))
        self.manualCustomerDouyin.setText(self.config_manager.api_config.get("抖店人工客服", ""))
        self.load_individual_settings()

    def load_individual_settings(self):
        current_ai = self.ai_selection_combo.currentText()
        if current_ai in self.config_manager.api_config["individual"]:
            individual_config = self.config_manager.api_config["individual"][current_ai]
            # 设置固定的API Base
            self.individual_api_base_edit.setText("http://ip.aijiakefu.com/v1")
            self.individual_key_edit.setText(individual_config.get("key", ""))
        else:
            # 设置固定的API Base
            self.individual_api_base_edit.setText("http://ip.aijiakefu.com/v1")
            self.individual_key_edit.setText("")

    def save_config(self):
        self.config_manager.api_config["username"] = self.username_edit.text()
        self.config_manager.api_config["人工客服"] = self.manualCustomerWeChat.text()
        self.config_manager.api_config["抖店人工客服"] = self.manualCustomerDouyin.text()

        # 使用固定的API Base，直接更新全局配置，不调用update_global_config避免弹窗
        self.config_manager.api_config["global"] = {
            "api_base": "http://ip.aijiakefu.com/v1",
            "key": self.global_key_edit.text()
        }
        
        # 直接更新个人配置，不调用update_individual_config避免弹窗
        if "individual" not in self.config_manager.api_config:
            self.config_manager.api_config["individual"] = {}
        
        self.config_manager.api_config["individual"][self.ai_selection_combo.currentText()] = {
            "api_base": "http://ip.aijiakefu.com/v1",
            "key": self.individual_key_edit.text()
        }
        
        # 保存配置
        self.config_manager.save_config()
        # 只在这里显示一次弹窗
        self.config_manager.show_message_box(QMessageBox.Information, "成功", "个人配置已成功保存。")
        
        print("self.api_config = self.config_manager.api_config前:", self.api_config)
        self.api_config = self.config_manager.api_config
        print("self.api_config = self.config_manager.api_config后:", self.api_config)

    def toggle(self):
        if not self.checkBox.isChecked() and not self.checkBox_douyin.isChecked() and not self.checkBox_doudian.isChecked() and not self.checkBox_qianniu.isChecked() and not self.checkBox_jingmai.isChecked() and not self.checkBox_pdd.isChecked() and not self.checkBox_kuaishou.isChecked() and not self.checkBox_zhima.isChecked():
            self.textBrowser.append(
                f"<div style='color:red;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 请勾选你需要自动化的平台<b></div>")
            return
        if self.checkBox.isChecked():
            if not self.manualCustomerWeChat.text():
                self.textBrowser.append(
                    f"<div style='color:red;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 请到设置页面填写你的人工客服昵称<b></div>")
                return

        global_config = self.config_manager.api_config.get("global", {})
        if not global_config.get("api_base") or not global_config.get("key"):
            self.textBrowser.append(
                f"<div style='color:red;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 请在配置中填写global下的api_base和key<b></div>")
            return

        if not self.is_running:  #False
            print("程序已启动", self.is_running)
            self.pushButton_yemian1.setIcon(self.icon3)  #开始中图标
            QTimer.singleShot(0, self.start)
        else:  #Ture
            print("程序已停止6", self.is_running)
            self.pushButton_yemian1.setIcon(self.icon1)  #待开始图标
            QTimer.singleShot(0, self.stop)
        self.is_running = not self.is_running

    def check_server_availability(self):
        servers_available = []
        self.parameter = {"CustomerName": self.manualCustomerWeChat.text()}  # 企微人类客服设置
        self.api_configsever = self.config_manager.get_server_config()  # 服务器配置
        self.multi_configmanage = self.duokaiconfig_manager.read_configs_to_dict()  # 多开配置
        print("self.multi_configmanage是", self.multi_configmanage)

        print("获取的服务器参数", self.api_configsever)

        self.parameter = {**self.api_configsever, **self.parameter, **self.api_config, **self.multi_configmanage}

        print("合并后企业微信参数", self.parameter)  # 服务器配置加客服昵称
        # 更新各服务器参数
        self.target_host_douyin = self.api_configsever.get("WebDriverServer_IP", "localhost")
        self.target_port_douyin = self.api_configsever.get("WebDriverServer_PORT", 9998)  # 修改为 9998
        self.target_host = self.api_configsever.get("WINDOWS_DRIVER_SERVER_IP", "localhost")
        self.target_port = self.api_configsever.get("WINDOWS_DRIVER_SERVER_PORT", 8888)
        self.target_host_doudian = self.target_host_douyin



        if self.checkBox.isChecked():

            server_status = check_port_available(self.target_host, self.target_port)
            # 检查百度OCR服务状态
            ocr_client = get_ocr_client()
            ocr_server_status, ocr_message = ocr_client.test_connection()

            if server_status:
                self.textBrowser.append(
                    f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> win服务器企业微信 {self.target_host}:{self.target_port} 可用<b></div>")
                if ocr_server_status:
                    servers_available.append("WindowsDriver")

                    self.textBrowser.append(
                        f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 百度OCR服务可用<b></div>")
                else:
                    self.textBrowser.append(
                        f"<div style='color:red;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 百度OCR服务不可用: {ocr_message}<b></div>")

            else:
                self.textBrowser.append(
                    f"<div style='color:red;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> win服务器企业微信 {self.target_host}:{self.target_port} 不可用<b></div>")

        if self.checkBox_douyin.isChecked():

            server_status = check_port_available(self.target_host_douyin, self.target_port_douyin)
            if server_status:
                servers_available.append("WebDriver")
                self.textBrowser.append(
                    f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> Web服务器抖音 {self.target_host_douyin}:{self.target_port_douyin} 可用<b></div>")
            else:
                self.textBrowser.append(
                    f"<div style='color:red;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> Web服务器抖音 {self.target_host_douyin}:{self.target_port_douyin} 不可用<b></div>")
        if self.checkBox_doudian.isChecked():

            server_status = check_port_available(self.target_host_doudian, self.target_port_doudian)
            if server_status:
                servers_available.append("DoudianWebDriver")
                self.textBrowser.append(
                    f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> Web服务器抖店 {self.target_host_doudian}:{self.target_port_doudian} 可用<b></div>")
            else:
                self.textBrowser.append(
                    f"<div style='color:red;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> Web服务器抖店 {self.target_host_doudian}:{self.target_port_doudian} 不可用<b></div>")
        if self.checkBox_jingmai.isChecked():

            server_status = check_port_available(self.target_host_JD, self.target_port_JD)
            if server_status:
                servers_available.append("JDWebDriver")
                self.textBrowser.append(
                    f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> Web服务器京东 {self.target_host_JD}:{self.target_port_JD} 可用<b></div>")
            else:
                self.textBrowser.append(
                    f"<div style='color:red;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> Web服务器京东 {self.target_host_JD}:{self.target_port_JD} 不可用<b></div>")
        if self.checkBox_pdd.isChecked():

            server_status = check_port_available(self.target_host_PDD, self.target_port_PDD)
            if server_status:
                servers_available.append("PDDWebDriver")
                self.textBrowser.append(
                    f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> Web服务器拼多多 {self.target_host_PDD}:{self.target_port_PDD} 可用<b></div>")
            else:
                self.textBrowser.append(
                    f"<div style='color:red;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> Web服务器拼多多 {self.target_host_PDD}:{self.target_port_PDD} 不可用<b></div>")

        if self.checkBox_kuaishou.isChecked(): # 快手

            server_status = check_port_available(self.target_host_KS, self.target_port_KS)
            if server_status:
                servers_available.append("KSWebDriver")
                self.textBrowser.append(
                    f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> Web服务器快手 {self.target_host_KS}:{self.target_port_KS} 可用<b></div>")
            else:
                self.textBrowser.append(
                    f"<div style='color:red;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> Web服务器快手 {self.target_host_KS}:{self.target_port_KS} 不可用<b></div>")

        if self.checkBox_zhima.isChecked(): # 芝麻

            server_status = check_port_available(self.target_host_zhima, self.target_port_zhima)
            if server_status:
                servers_available.append("zhimaWebDriver")
                self.textBrowser.append(
                    f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> Web服务器芝麻小客服 {self.target_host_zhima}:{self.target_port_zhima} 可用<b></div>")
            else:
                self.textBrowser.append(
                    f"<div style='color:red;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> Web服务器芝麻小客服 {self.target_host_zhima}:{self.target_port_zhima} 不可用<b></div>")



        if self.checkBox_qianniu.isChecked():

            server_status = check_port_available(self.qianniu_target_host, self.qianniu_target_port)
            # 检查百度OCR服务状态
            ocr_client = get_ocr_client()
            ocr_server_status, ocr_message = ocr_client.test_connection()

            if server_status:

                self.textBrowser.append(
                    f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> win服务器千牛 {self.qianniu_target_host}:{self.qianniu_target_port} 可用<b></div>")
                if ocr_server_status:
                    servers_available.append("qianniuWinDriver")

                    self.textBrowser.append(
                        f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 百度OCR服务可用<b></div>")
                else:
                    self.textBrowser.append(
                        f"<div style='color:red;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 百度OCR服务不可用: {ocr_message}<b></div>")
            else:
                self.textBrowser.append(
                    f"<div style='color:red;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> win服务器千牛 {self.qianniu_target_host}:{self.qianniu_target_port} 不可用<b></div>")
        return servers_available

    def start(self):
        try:

            servers_available = self.check_server_availability()

            if not servers_available:
                self.textBrowser.append(
                    f"<div style='color:red;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 所有服务器都不可用，终止启动<b></div>")
                self.pushButton_yemian1.setIcon(self.icon1)  #切换到待开始按键(Play Icon)
                self.is_running = False
                return
            if "WindowsDriver" in servers_available:
                self.WindowsDriver = WindowsDriver(self.proxy_host, self.proxy_port, self.parameter)
                self.WindowsDriver.start()
                self.textBrowser.append(
                    f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 启动企业微信驱动程序成功<b></div>")
            if "qianniuWinDriver" in servers_available:
                self.qianniu_WindowsDriver = WindowsDriver(self.qianniu_proxy_host, self.qianniu_proxy_port, self.parameter)
                self.qianniu_WindowsDriver.start()
                self.textBrowser.append(
                    f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 启动千牛驱动程序成功<b></div>")
            if "WebDriver" in servers_available:
                # 检测 Google Chrome 是否安装
                if not browser_utils.is_chrome_installed():
                    browser_utils.show_chrome_not_installed_message()
                    self.is_running = False
                    self.pushButton_yemian1.setIcon(self.icon1)
                    return

                # 检测并关闭浏览器
                if browser_utils.check_and_close_browser() is False:
                    self.is_running = False
                    self.pushButton_yemian1.setIcon(self.icon1)
                    return

                self.WebDriver = JDWebDriver(self.proxy_host_douyin, self.proxy_port_douyin, self.parameter, self.douyin_debug_port, self.user_data_dir_douyin)
                self.WebDriver.start()
                self.textBrowser.append(
                    f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 启动抖音驱动程序成功<b></div>")
            if "DoudianWebDriver" in servers_available:
                print("抖店开启之前的参数", self.parameter)
                self.Doudianweb_driver = DoudianWebDriver(self.proxy_host_doudian, self.proxy_port_doudian, self.parameter, self.Doudian_debug_port, self.user_data_dir)
                self.Doudianweb_driver.start()
                self.textBrowser.append(
                    f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 启动抖店驱动程序成功<b></div>")

            if "JDWebDriver" in servers_available:
                print("京东开启之前的参数", self.parameter)
                self.JD_web_driver = JDWebDriver(self.proxy_host_JD, self.proxy_port_JD, self.parameter, self.JD_debug_port, self.user_data_dir_JD)
                self.JD_web_driver.start()
                self.textBrowser.append(
                    f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 启动京东驱动程序成功<b></div>")

            if "PDDWebDriver" in servers_available:
                # PDDWebDriver存在就启动

                self.PDD_web_driver = JDWebDriver(self.proxy_host_PDD, self.proxy_port_PDD, self.parameter, self.PDD_debug_port, self.user_data_dir_PDD)
                self.PDD_web_driver.start()
                self.textBrowser.append(
                    f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 启动拼多多驱动程序成功<b></div>")

            if "KSWebDriver" in servers_available:

                self.KS_web_driver = JDWebDriver(self.proxy_host_KS, self.proxy_port_KS, self.parameter, self.KS_debug_port, self.user_data_dir_KS)
                self.KS_web_driver.start()
                self.textBrowser.append(
                    f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 启动快手驱动程序成功<b></div>")

            if "zhimaWebDriver" in servers_available:

                self.zhima_web_driver = JDWebDriver(self.proxy_host_zhima, self.proxy_port_zhima, self.parameter, self.zhima_debug_port, self.user_data_dir_zhima)
                self.zhima_web_driver.start()
                self.textBrowser.append(
                    f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 启动芝麻小客服驱动程序成功<b></div>")




            self.textBrowser.append(
                f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> Ai全局自动化已运行, 请勿人为干扰程序运行<b></div>")

            self.ClientLocalServer.ClietServerMain()
            self.qianniu_ClientLocalServer.ClietServerMain()

            self.ClientLocalServerDouyin.ClietServerMain()
            self.ClientLocalServerDoudian.ClietServerMain()
            self.ClientLocalServerJD.ClietServerMain()
            self.ClientLocalServerPDD.ClietServerMain()
            self.ClientLocalServerKS.ClietServerMain()
            self.ClientLocalServerzhima.ClietServerMain()


        except Exception as e:
            self.textBrowser.append(
                f"<div style='color:red;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 启动全局自动化失败, 请联系客服处理: {e}<b></div>")

    def stop(self):
        self.textBrowser.append(
            f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> Ai全局自动化已关闭<b></div>")
        self.ClientLocalServer.ServerClose()
        self.ClientLocalServerDouyin.ServerClose()
        self.ClientLocalServerDoudian.ServerClose()
        self.ClientLocalServerPDD.ServerClose()
        self.ClientLocalServerKS.ServerClose()
        self.ClientLocalServerzhima.ServerClose()
        self.ClientLocalServerJD.ServerClose()
        self.qianniu_ClientLocalServer.ServerClose()

    def on_checkbox_state_changed(self, checkbox, start_func, stop_func):
        # 只有在 self.is_running 为 True 时，才会执行服务启动或停止的逻辑
        if self.is_running:
            self.update_service(checkbox, start_func, stop_func)

    def update_service(self, checkbox, start_func, stop_func):
        # 检查复选框的状态并启动或停止对应服务
        if checkbox.isChecked():
            start_func()
        else:
            stop_func()

        # 检查所有复选框是否都未选中，如果是，则停止整个流程
        checkboxes = [
            self.checkBox,
            self.checkBox_douyin,
            self.checkBox_doudian,
            self.checkBox_qianniu,
            self.checkBox_jingmai,
            self.checkBox_pdd,
            self.checkBox_kuaishou
        ]
        if not any(cb.isChecked() for cb in checkboxes):
            self.is_running = False
            self.pushButton_yemian1.setIcon(self.icon1)

    def start_wechat(self):
        self.textBrowser.append(
            f"<div style='color:orange;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 正在启动企业微信驱动程序<b></div>")
        self.WindowsDriver = WindowsDriver(self.proxy_host, self.proxy_port, self.parameter)
        self.WindowsDriver.start()
        self.ClientLocalServer.ClietServerMain()
        self.textBrowser.append(
            f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 启动企业微信驱动程序成功<b></div>")

    def stop_wechat(self):
        self.textBrowser.append(
            f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 停止企业微信驱动程序<b></div>")
        self.ClientLocalServer.ServerClose()


    def start_qianniu(self):
        self.textBrowser.append(
            f"<div style='color:orange;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 正在启动千牛驱动程序<b></div>")
        self.qianniu_WindowsDriver = WindowsDriver(self.qianniu_proxy_host, self.qianniu_proxy_port, self.parameter)
        self.qianniu_WindowsDriver.start()
        self.qianniu_ClientLocalServer.ClietServerMain()
        self.textBrowser.append(
            f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 启动千牛驱动程序成功<b></div>")

    def stop_qianniu(self):
        self.textBrowser.append(
            f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 停止千牛驱动程序<b></div>")
        self.qianniu_ClientLocalServer.ServerClose()

    def start_douyin(self):
        self.textBrowser.append(
            f"<div style='color:orange;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 正在启动抖音驱动程序<b></div>")
        if browser_utils.check_and_close_browser() is False:
            self.is_running = False
            self.pushButton_yemian1.setIcon(self.icon1)
            return
        self.WebDriver = JDWebDriver(self.proxy_host_douyin, self.proxy_port_douyin, self.parameter, self.douyin_debug_port, self.user_data_dir_douyin)
        self.WebDriver.start()
        self.ClientLocalServerDouyin.ClietServerMain()
        self.textBrowser.append(
            f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 启动抖音驱动程序成功<b></div>")

    def start_doudian(self):
        self.textBrowser.append(
            f"<div style='color:orange;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 正在启动抖店驱动程序<b></div>")
        self.Doudianweb_driver = DoudianWebDriver(self.proxy_host_doudian, self.proxy_port_doudian, self.parameter,
                                                  self.Doudian_debug_port, self.user_data_dir)
        self.Doudianweb_driver.start()
        self.ClientLocalServerDoudian.ClietServerMain()
        self.textBrowser.append(
            f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 启动抖店驱动程序成功<b></div>")

    def start_JD(self):
        self.textBrowser.append(
            f"<div style='color:orange;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 正在启动京麦驱动程序<b></div>")
        self.JD_web_driver = JDWebDriver(self.proxy_host_JD, self.proxy_port_JD, self.parameter,
                                                  self.JD_debug_port, self.user_data_dir_JD)
        self.JD_web_driver.start()
        self.ClientLocalServerDoudian.ClietServerMain()
        self.textBrowser.append(
            f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 启动京麦驱动程序成功<b></div>")

    def start_PDD(self):
        self.textBrowser.append(
            f"<div style='color:orange;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 正在启动拼多多驱动程序<b></div>")
        self.PDD_web_driver = JDWebDriver(self.proxy_host_PDD, self.proxy_port_PDD, self.parameter,
                                         self.PDD_debug_port, self.user_data_dir_PDD)
        self.PDD_web_driver.start()
        self.ClientLocalServerDoudian.ClietServerMain()
        self.textBrowser.append(
            f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 启动拼多多驱动程序成功<b></div>")

    def start_KS(self):
        self.textBrowser.append(
            f"<div style='color:orange;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 正在启动快手驱动程序<b></div>")
        self.KS_web_driver = JDWebDriver(self.proxy_host_KS, self.proxy_port_KS, self.parameter,
                                         self.KS_debug_port, self.user_data_dir_KS)
        self.KS_web_driver.start()
        self.ClientLocalServerKS.ClietServerMain()
        self.textBrowser.append(
            f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 启动快手驱动程序成功<b></div>")


    def start_zhima(self):
        self.textBrowser.append(
            f"<div style='color:orange;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 正在启动芝麻小客服驱动程序<b></div>")
        self.zhima_web_driver = JDWebDriver(self.proxy_host_zhima, self.proxy_port_zhima, self.parameter,
                                         self.zhima_debug_port, self.user_data_dir_zhima)
        self.zhima_web_driver.start()
        self.ClientLocalServerzhima.ClietServerMain()
        self.textBrowser.append(
            f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 启动芝麻小客服驱动程序成功<b></div>")



    def stop_douyin(self):
        self.textBrowser.append(
            f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 停止抖店驱动程序<b></div>")
        self.ClientLocalServerDouyin.ServerClose()

    def stop_doudian(self):
        self.textBrowser.append(
            f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 停止抖音驱动程序<b></div>")
        self.ClientLocalServerDoudian.ServerClose()
    def stop_JD(self):
        self.textBrowser.append(
            f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 停止京麦驱动程序<b></div>")
        self.ClientLocalServerJD.ServerClose()

    def stop_PDD(self):
        self.textBrowser.append(
            f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 停止拼多多驱动程序<b></div>")
        self.ClientLocalServerPDD.ServerClose()
        # self.ClientLocalServerPDD.ServerClose()

    def stop_KS(self):
        self.textBrowser.append(
            f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 停止快手驱动程序<b></div>")
        self.ClientLocalServerKS.ServerClose()



    def stop_zhima(self):
        self.textBrowser.append(
            f"<div style='color:green;font-size:13px'><b>{time.strftime(r'%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}-> 停止芝麻小客服驱动程序<b></div>")
        self.ClientLocalServerzhima.ServerClose()
    def closeEvent(self, event):
        self.ClientLocalServer.ServerClose()



    def set_user_info(self, username, avatar_url, role):
        self.username_label.setStyleSheet("color: black;")
        self.username_label.setText(f"你好, {username}")
        self.username_label.adjustSize()
        self.username_label.show()
        self.lineEdit_7.setText(username)
        self.lineEdit_7.setReadOnly(True)
        self.show()


if __name__ == "__main__":
    app = QApplication(sys.argv)

    # 跳过登录
    main_window = AiJakeFuClient()
    main_window.show()

    # 登录
    # auth_manager = AuthManager()
    # login_window = LoginWindow(auth_manager, show_qr_code=False)
    # login_window.auth_manager.login_successful.connect(lambda username, avatar_url, role: AiJakeFuClient().set_user_info(username, avatar_url, role))
    # login_window.show()

    sys.exit(app.exec_())
