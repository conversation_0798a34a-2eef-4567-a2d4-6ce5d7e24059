#Tool/wechat_api.py
import requests

APP_ID = 'wxb3491961d817ce9b'
APP_SECRET = '055d19783c91719e69e6d450930dc57f'

def get_access_token():
    url = f'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={APP_ID}&secret={APP_SECRET}'
    response = requests.get(url)
    return response.json()['access_token']

def create_qr_code(access_token):
    url = f'https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token={access_token}'
    data = {
        "expire_seconds": 3600,
        "action_name": "QR_SCENE",
        "action_info": {
            "scene": {
                "scene_id": 123
            }
        }
    }
    response = requests.post(url, json=data)
    return response.json()['ticket']

def get_qr_code_url(ticket):
    return f'https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket={ticket}'

