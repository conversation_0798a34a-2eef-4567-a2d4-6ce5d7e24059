# AIplatform.py
from PyQt5 import QtCore, QtGui, QtWidgets
from PyQt5.QtGui import QFont
import os

class Platform(QtWidgets.QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setupUi()

    def setupUi(self):
        # 应用商店
        self.labelyinyingshangdian = QtWidgets.QLabel(self)
        self.labelyinyingshangdian.setGeometry(QtCore.QRect(20, 120, 81, 41))
        self.labelyinyingshangdian.setObjectName("labelyinyingshangdian")

        # 企微开始
        self.checkBox = QtWidgets.QCheckBox(self)
        self.checkBox.setGeometry(QtCore.QRect(30, 170-50, 131, 121))
        font = QtGui.QFont()
        font.setPointSize(13)
        self.checkBox.setFont(font)
        self.checkBox.setText("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(f"{os.getcwd()}/Static/ProgramImage/wework.png"), QtGui.QIcon.Normal,
                       QtGui.QIcon.Off)
        self.checkBox.setIcon(icon)
        self.checkBox.setIconSize(QtCore.QSize(50, 50))
        self.checkBox.setTristate(False)
        self.checkBox.setObjectName("checkBox")
        self.checkBox.setChecked(True)  # 设置单选框默认勾上

        # 抖音私信开始
        self.checkBox_douyin = QtWidgets.QCheckBox(self)
        self.checkBox_douyin.setGeometry(QtCore.QRect(30 + col_spacing, 170-50, 131, 121))  # 调整位置
        font = QtGui.QFont()
        font.setPointSize(13)
        self.checkBox_douyin.setFont(font)
        self.checkBox_douyin.setText("")
        icon_douyin = QtGui.QIcon()
        icon_douyin.addPixmap(QtGui.QPixmap(f"{os.getcwd()}/Static/ProgramImage/douyin.png"), QtGui.QIcon.Normal,
                              QtGui.QIcon.Off)
        self.checkBox_douyin.setIcon(icon_douyin)
        self.checkBox_douyin.setIconSize(QtCore.QSize(50, 50))
        self.checkBox_douyin.setTristate(False)
        self.checkBox_douyin.setObjectName("checkBox_douyin")
        self.checkBox_douyin.setChecked(True)  # 设置单选框默认勾上

        # 支持平台Logo
        # 包含多个复选框，分别对应不同的平台Logo，如doudian.png、qianniu.png、pdd.png、wechat.png、xhs.png、qianfan.png、jingmai.png和last.png