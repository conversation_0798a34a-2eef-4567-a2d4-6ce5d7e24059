import base64
import json
import subprocess



class DriveDouyin:
    def start_server(self, server_ip, server_port, parameter):

        json_str = json.dumps(parameter, ensure_ascii=False)
        print("处理后的 JSON 字符串:")
        # print(server_ip,":",server_port)
        print(json_str)
        # encoded_str = base64.b64encode(json_str.encode('utf-8')).decode('utf-8')
        # print("\n编码后的 Base64 字符串:")
        # print("parameter是：",parameter)
        # print("str(parameter)后：",str(parameter))
        driver_params = {
            "serverIp": server_ip,
            "serverPort": server_port,
            "browserName": "chrome",
            "debugPort": 8989,
            "userDataDir": "./UserData",
            "browserPath": None,
            "argument": None,
            "extendParam": json_str
        }

        default_params = json.dumps(driver_params, ensure_ascii=False)

        # 使用 subprocess 启动 WebDriver.exe，并传递参数
        command = ["WebDriver.exe", default_params]
        process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

        # 获取进程的标准输出和错误输出
        stdout, stderr = process.communicate()

        # 处理输出
        print("Standard Output:", stdout.decode())
        print("Standard Error:", stderr.decode())

# 测试启动服务器
if __name__ == "__main__":
    douyin = DriveDouyin()
    douyin.start_server("***************", 9998, {"key": "value"})