import os
from PyQt5 import QtWidgets, Qt<PERSON><PERSON>, QtGui
from PyQt5.QtCore import Qt

def sdlogo(self):
    # 应用商店
    self.labelyinyingshangdian = QtWidgets.QLabel(self.page)
    self.labelyinyingshangdian.setGeometry(QtCore.QRect(20, 120, 81, 41))
    self.labelyinyingshangdian.setObjectName("labelyinyingshangdian")

    # 企微开始
    self.checkBox = QtWidgets.QCheckBox(self.page)
    self.checkBox.setGeometry(QtCore.QRect(30, 120, 131, 121))
    font = QtGui.QFont()
    font.setPointSize(13)
    self.checkBox.setFont(font)
    self.checkBox.setText("")
    icon = QtGui.QIcon()
    icon.addPixmap(QtGui.QPixmap(f"{os.getcwd()}/Static/ProgramImage/wework.png"), QtGui.QIcon.Normal,
                   QtGui.QIcon.Off)
    self.checkBox.setIcon(icon)
    self.checkBox.setIconSize(QtCore.QSize(50, 50))
    self.checkBox.setTristate(False)
    self.checkBox.setObjectName("checkBox")
    self.checkBox.setChecked(False)  # 设置单选框默认勾上
    # 企微结束

    row_spacing = 80  # 调整行距
    col_spacing = 100  # 调整列距

    # 抖音私信开始
    self.checkBox_douyin = QtWidgets.QCheckBox(self.page)
    self.checkBox_douyin.setGeometry(QtCore.QRect(30 + col_spacing, 120, 131, 121))  # 调整位置
    font = QtGui.QFont()
    font.setPointSize(13)
    self.checkBox_douyin.setFont(font)
    self.checkBox_douyin.setText("")
    icon_douyin = QtGui.QIcon()
    icon_douyin.addPixmap(QtGui.QPixmap(f"{os.getcwd()}/Static/ProgramImage/douyin.png"), QtGui.QIcon.Normal,
                          QtGui.QIcon.Off)
    self.checkBox_douyin.setIcon(icon_douyin)
    self.checkBox_douyin.setIconSize(QtCore.QSize(50, 50))
    self.checkBox_douyin.setTristate(False)
    self.checkBox_douyin.setObjectName("checkBox_douyin")
    self.checkBox_douyin.setChecked(False)  # 设置单选框默认勾上

    # 添加按钮抖音多开
    self.button_douyin_multi_open = QtWidgets.QPushButton(self.page)
    self.button_douyin_multi_open.setGeometry(QtCore.QRect( 2 * col_spacing-5 , 168, 28, 25))  # 调整位置和大小
    self.button_douyin_multi_open.setText("多开")
    self.button_douyin_multi_open.setObjectName("button_douyin_multi_open")
    self.button_douyin_multi_open.clicked.connect(self.show_douyin_multi_instance_dialog)

    # 抖音私信结束

    # 添加抖店 doudian.png
    self.checkBox_doudian = QtWidgets.QCheckBox(self.page)
    self.checkBox_doudian.setGeometry(QtCore.QRect(30 + 2 * col_spacing, 120, 131, 121))  # 调整位置
    font = QtGui.QFont()
    font.setPointSize(13)
    self.checkBox_doudian.setFont(font)
    self.checkBox_doudian.setText("")
    icon_doudian = QtGui.QIcon()
    icon_doudian.addPixmap(QtGui.QPixmap(f"{os.getcwd()}/Static/ProgramImage/doudian.png"), QtGui.QIcon.Normal,
                           QtGui.QIcon.Off)
    self.checkBox_doudian.setIcon(icon_doudian)
    self.checkBox_doudian.setIconSize(QtCore.QSize(50, 50))
    self.checkBox_doudian.setTristate(False)
    self.checkBox_doudian.setObjectName("checkBox_doudian")
    self.checkBox_doudian.setChecked(False)  # 设置单选框默认勾上
    # self.checkBox_doudian.setEnabled(False)  # 注释掉就能开启

    # 添加按钮抖店多开
    self.button_doudian_multi_open = QtWidgets.QPushButton(self.page)
    self.button_doudian_multi_open.setGeometry(QtCore.QRect(30 + 2 * col_spacing + 63, 120 + 48, 28, 25))  # 调整位置和大小
    self.button_doudian_multi_open.setText("多开")
    self.button_doudian_multi_open.setObjectName("button_doudian_multi_open")
    self.button_doudian_multi_open.clicked.connect(self.show_multi_instance_dialog)
    # self.layout.addWidget(self.button_doudian_multi_open, alignment=Qt.AlignCenter)

    # 添加 qianniu.png
    self.checkBox_qianniu = QtWidgets.QCheckBox(self.page)
    self.checkBox_qianniu.setGeometry(QtCore.QRect(30 + 3 * col_spacing, 120, 131, 121))  # 调整位置
    font = QtGui.QFont()
    font.setPointSize(13)
    self.checkBox_qianniu.setFont(font)
    self.checkBox_qianniu.setText("")
    icon_qianniu = QtGui.QIcon()
    icon_qianniu.addPixmap(QtGui.QPixmap(f"{os.getcwd()}/Static/ProgramImage/qianniu.png"), QtGui.QIcon.Normal,
                           QtGui.QIcon.Off)
    self.checkBox_qianniu.setIcon(icon_qianniu)
    self.checkBox_qianniu.setIconSize(QtCore.QSize(50, 50))
    self.checkBox_qianniu.setTristate(False)
    self.checkBox_qianniu.setObjectName("checkBox_qianniu")
    self.checkBox_qianniu.setChecked(False)  # 设置单选框默认勾上
    # self.checkBox_qianniu.setEnabled(False)  # 注释掉就能开启

    # 添加 pdd.png
    self.checkBox_pdd = QtWidgets.QCheckBox(self.page)
    self.checkBox_pdd.setGeometry(QtCore.QRect(30 + 4 * col_spacing, 120, 131, 121))  # 调整位置
    font = QtGui.QFont()
    font.setPointSize(13)
    self.checkBox_pdd.setFont(font)
    self.checkBox_pdd.setText("")
    icon_pdd = QtGui.QIcon()
    icon_pdd.addPixmap(QtGui.QPixmap(f"{os.getcwd()}/Static/ProgramImage/pdd.png"), QtGui.QIcon.Normal,
                       QtGui.QIcon.Off)
    self.checkBox_pdd.setIcon(icon_pdd)
    self.checkBox_pdd.setIconSize(QtCore.QSize(50, 50))
    self.checkBox_pdd.setTristate(False)
    self.checkBox_pdd.setObjectName("checkBox_pdd")
    self.checkBox_pdd.setChecked(False)  # 设置单选框默认勾上
    # self.checkBox_pdd.setEnabled(False)  # 注释掉就能开启

    # 拼多多多开
    self.button_PDD_multi_open = QtWidgets.QPushButton(self.page)
    self.button_PDD_multi_open.setGeometry(QtCore.QRect(30 + 2 * col_spacing + 263, 166, 28, 25))  # 调整位置和大小
    self.button_PDD_multi_open.setText("多开")
    self.button_PDD_multi_open.setObjectName("button_PDD_multi_open")
    self.button_PDD_multi_open.clicked.connect(self.show_PDD_multi_instance_dialog)



    # 添加 wechat.png
    self.checkBox_wechat = QtWidgets.QCheckBox(self.page)
    self.checkBox_wechat.setGeometry(QtCore.QRect(30, 120 + row_spacing, 131, 121))  # 调整位置
    font = QtGui.QFont()
    font.setPointSize(13)
    self.checkBox_wechat.setFont(font)
    self.checkBox_wechat.setText("")
    icon_wechat = QtGui.QIcon()
    icon_wechat.addPixmap(QtGui.QPixmap(f"{os.getcwd()}/Static/ProgramImage/wechat.png"), QtGui.QIcon.Normal,
                          QtGui.QIcon.Off)
    self.checkBox_wechat.setIcon(icon_wechat)
    self.checkBox_wechat.setIconSize(QtCore.QSize(50, 50))
    self.checkBox_wechat.setTristate(False)
    self.checkBox_wechat.setObjectName("checkBox_wechat")
    self.checkBox_wechat.setChecked(False)  # 设置单选框默认勾上
    self.checkBox_wechat.setEnabled(False)  # 注释掉就能开启

    # 添加 xhs.png
    self.checkBox_xhs = QtWidgets.QCheckBox(self.page)
    self.checkBox_xhs.setGeometry(QtCore.QRect(30 + col_spacing, 120 + row_spacing, 131, 121))  # 调整位置
    font = QtGui.QFont()
    font.setPointSize(13)
    self.checkBox_xhs.setFont(font)
    self.checkBox_xhs.setText("")
    icon_xhs = QtGui.QIcon()
    icon_xhs.addPixmap(QtGui.QPixmap(f"{os.getcwd()}/Static/ProgramImage/xhs.png"), QtGui.QIcon.Normal,
                       QtGui.QIcon.Off)
    self.checkBox_xhs.setIcon(icon_xhs)
    self.checkBox_xhs.setIconSize(QtCore.QSize(50, 50))
    self.checkBox_xhs.setTristate(False)
    self.checkBox_xhs.setObjectName("checkBox_xhs")
    self.checkBox_xhs.setChecked(False)  # 设置单选框默认勾上
    self.checkBox_xhs.setEnabled(False)  # 注释掉就能开启

    # 添加 qianfan.png
    self.checkBox_qianfan = QtWidgets.QCheckBox(self.page)
    self.checkBox_qianfan.setGeometry(QtCore.QRect(30 + 2 * col_spacing, 120 + row_spacing, 131, 121))  # 调整位置
    font = QtGui.QFont()
    font.setPointSize(13)
    self.checkBox_qianfan.setFont(font)
    self.checkBox_qianfan.setText("")
    icon_qianfan = QtGui.QIcon()
    icon_qianfan.addPixmap(QtGui.QPixmap(f"{os.getcwd()}/Static/ProgramImage/qianfan.png"), QtGui.QIcon.Normal,
                           QtGui.QIcon.Off)
    self.checkBox_qianfan.setIcon(icon_qianfan)
    self.checkBox_qianfan.setIconSize(QtCore.QSize(50, 50))
    self.checkBox_qianfan.setTristate(False)
    self.checkBox_qianfan.setObjectName("checkBox_qianfan")
    self.checkBox_qianfan.setChecked(False)  # 设置单选框默认勾上
    self.checkBox_qianfan.setEnabled(False)  # 注释掉就能开启

    # 添加 jingmai.png
    self.checkBox_jingmai = QtWidgets.QCheckBox(self.page)
    self.checkBox_jingmai.setGeometry(QtCore.QRect(30 + 3 * col_spacing, 120 + row_spacing, 131, 121))  # 调整位置
    font = QtGui.QFont()
    font.setPointSize(13)
    self.checkBox_jingmai.setFont(font)
    self.checkBox_jingmai.setText("")
    icon_jingmai = QtGui.QIcon()
    icon_jingmai.addPixmap(QtGui.QPixmap(f"{os.getcwd()}/Static/ProgramImage/jingmai.png"), QtGui.QIcon.Normal,
                           QtGui.QIcon.Off)
    self.checkBox_jingmai.setIcon(icon_jingmai)
    self.checkBox_jingmai.setIconSize(QtCore.QSize(50, 50))
    self.checkBox_jingmai.setTristate(False)
    self.checkBox_jingmai.setObjectName("checkBox_jingmai")
    self.checkBox_jingmai.setChecked(False)  # 设置单选框默认勾上
    # self.checkBox_jingmai.setEnabled(False)  # 注释掉就能开启

    # 京东多开
    self.button_JD_multi_open = QtWidgets.QPushButton(self.page)
    self.button_JD_multi_open.setGeometry(QtCore.QRect(30 + 2 * col_spacing + 163, 246, 28, 25))  # 调整位置和大小
    self.button_JD_multi_open.setText("多开")
    self.button_JD_multi_open.setObjectName("button_JD_multi_open")
    self.button_JD_multi_open.clicked.connect(self.show_JD_multi_instance_dialog)





    # 添加最后一个控件
    self.checkBox_kuaishou = QtWidgets.QCheckBox(self.page)
    self.checkBox_kuaishou.setGeometry(QtCore.QRect(30 + 4 * col_spacing, 120 + row_spacing, 131, 121))  # 调整位置
    font = QtGui.QFont()
    font.setPointSize(13)
    self.checkBox_kuaishou.setFont(font)
    self.checkBox_kuaishou.setText("")
    icon_kuaishou = QtGui.QIcon()
    icon_kuaishou.addPixmap(QtGui.QPixmap(f"{os.getcwd()}/Static/ProgramImage/kuaishou.png"), QtGui.QIcon.Normal,
                            QtGui.QIcon.Off)
    self.checkBox_kuaishou.setIcon(icon_kuaishou)
    self.checkBox_kuaishou.setIconSize(QtCore.QSize(50, 50))
    self.checkBox_kuaishou.setTristate(False)
    self.checkBox_kuaishou.setObjectName("checkBox_last")
    self.checkBox_kuaishou.setChecked(False)  # 设置单选框默认勾上

    # 快手多开
    self.button_KS_multi_open = QtWidgets.QPushButton(self.page)
    self.button_KS_multi_open.setGeometry(QtCore.QRect(30 + 2 * col_spacing + 263, 246, 28, 25))  # 调整位置和大小
    self.button_KS_multi_open.setText("多开")
    self.button_KS_multi_open.setObjectName("button_KS_multi_open")
    self.button_KS_multi_open.clicked.connect(self.show_KS_multi_instance_dialog)

    # 添加 zhima.png 小芝麻客服
    self.checkBox_zhima = QtWidgets.QCheckBox(self.page)
    self.checkBox_zhima.setGeometry(QtCore.QRect(30, 120 + 2*row_spacing, 131, 121))  # 调整位置
    font = QtGui.QFont()
    font.setPointSize(13)
    self.checkBox_zhima.setFont(font)
    self.checkBox_zhima.setText("")
    icon_wechat = QtGui.QIcon()
    icon_wechat.addPixmap(QtGui.QPixmap(f"{os.getcwd()}/Static/ProgramImage/zhima.png"), QtGui.QIcon.Normal,
                          QtGui.QIcon.Off)
    self.checkBox_zhima.setIcon(icon_wechat)
    self.checkBox_zhima.setIconSize(QtCore.QSize(50, 50))
    self.checkBox_zhima.setTristate(False)
    self.checkBox_zhima.setObjectName("checkBox_zhima")
    self.checkBox_zhima.setChecked(False)  # 设置单选框默认勾上

    # 快手多开
    self.button_zhima_multi_open = QtWidgets.QPushButton(self.page)
    self.button_zhima_multi_open.setGeometry(QtCore.QRect( 1 * col_spacing , 326, 28, 25))  # 调整位置和大小
    self.button_zhima_multi_open.setText("多开")
    self.button_zhima_multi_open.setObjectName("button_zhima_multi_open")
    self.button_zhima_multi_open.clicked.connect(self.show_zhima_multi_instance_dialog)





    # self.checkBox_zhima.setEnabled(False)  # 注释掉就能开启

    # self.checkBox_kuaishou.setEnabled(False)  # 注释掉就能开启
    # -------------------支持平台Logo结束-------------------
