#Tool\ui.py
from PyQt5 import QtCore, QtGui, QtWidgets
import os
from Tool.ClietServer import ProxyConfigPage
from Tool.multi_instance_control_window import MultiInstanceControlWindow
from Tool.ui_shangbiao import sdlogo
from Tool.ui_setpage import setpage
from Tool.ui_userpage import usersetpage
from Tool.version_updater import LOCAL_VERSION  # 引入 LOCAL_VERSION
from Tool.doudianmultidialog import MultiInstanceDialog
from Tool.JDmultidialog import JDMultiInstanceDialog
from Tool.PDDmultidialog import PDDMultiInstanceDialog
from Tool.KSmultidialog import KSMultiInstanceDialog
from Tool.zhimamultidialog import zhimaMultiInstanceDialog
from Tool.douyinmultidialog import douyinMultiInstanceDialog
from PyQt5.QtWidgets import  QDialog
class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        MainWindow.setObjectName("MainWindow")
        MainWindow.resize(700, 700)
        self.centralwidget = QtWidgets.QWidget(MainWindow)
        self.centralwidget.setObjectName("centralwidget")
        self.stackedWidget = QtWidgets.QStackedWidget(self.centralwidget)
        self.stackedWidget.setGeometry(QtCore.QRect(100, 0, 521, 650))
        self.stackedWidget.setObjectName("stackedWidget")
        # 添加版本号标签
        self.version_label = QtWidgets.QLabel(self.centralwidget)
        self.version_label.setGeometry(QtCore.QRect(600, 650, 150, 30))  # 调整位置和大小
        self.version_label.setObjectName("version_label")
        self.version_label.setText(f"版本: {LOCAL_VERSION}")


        self.page = QtWidgets.QWidget()#界面首页
        self.page.setObjectName("page")

        print("开始运行ProxyConfigPage")
        # 初始化 proxy_config_page
        self.proxy_config_page = ProxyConfigPage()
        print("结束运行ProxyConfigPage")

        self.proxy_config_page.setWindowTitle("服务器配置")
        self.proxy_config_page.setGeometry(100, 100, 400, 600)

        sdlogo(self)#支持的logo

        self.pushButton_yemian1 = QtWidgets.QPushButton(self.page)#开始按钮
        self.pushButton_yemian1.setGeometry(QtCore.QRect(60, 40, 35, 35))
        self.pushButton_yemian1.setStyleSheet("QPushButton {\n"
"                background-color: rgba(0, 0, 0, 0);\n"
"            }")
        self.pushButton_yemian1.setText("")
        self.icon1 = QtGui.QIcon()
        self.icon1.addPixmap(QtGui.QPixmap(f"{os.getcwd()}/Static/ProgramImage/play.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.pushButton_yemian1.setIcon(self.icon1)
        self.pushButton_yemian1.setIconSize(QtCore.QSize(35, 35))
        self.pushButton_yemian1.setObjectName("pushButton_yemian1")

        self.pushButton_yemian2 = QtWidgets.QPushButton(self.page)
        self.pushButton_yemian2.setGeometry(QtCore.QRect(210, 40, 35, 35))
        self.pushButton_yemian2.setStyleSheet("QPushButton {\n"
"                background-color: rgba(0, 0, 0, 0);\n"
"            }")
        self.pushButton_yemian2.setText("")#设置按钮
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(f"{os.getcwd()}/Static/ProgramImage/setting.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.pushButton_yemian2.setIcon(icon2)
        self.pushButton_yemian2.setIconSize(QtCore.QSize(35, 35))
        self.pushButton_yemian2.setObjectName("pushButton_yemian2")


        self.icon3 = QtGui.QIcon()
        self.icon3.addPixmap(QtGui.QPixmap(f"{os.getcwd()}/Static/ProgramImage/stop.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)

        self.pushButton_yemian3 = QtWidgets.QPushButton(self.page)#用户中心按钮
        self.pushButton_yemian3.setGeometry(QtCore.QRect(360, 40, 35, 35))
        self.pushButton_yemian3.setStyleSheet("QPushButton {\n"
"                background-color: rgba(0, 0, 0, 0);\n"
"            }")
        self.pushButton_yemian3.setText("")
        icon4 = QtGui.QIcon()
        icon4.addPixmap(QtGui.QPixmap(f"{os.getcwd()}/Static/ProgramImage/account.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.pushButton_yemian3.setIcon(icon4)
        self.pushButton_yemian3.setIconSize(QtCore.QSize(35, 35))
        self.pushButton_yemian3.setAutoDefault(False)
        self.pushButton_yemian3.setDefault(False)
        self.pushButton_yemian3.setFlat(False)
        self.pushButton_yemian3.setObjectName("pushButton_yemian3")

        # 调整“运行日志”标签的位置
        self.label_2 = QtWidgets.QLabel(self.page)
        self.label_2.setGeometry(QtCore.QRect(20, 410, 81, 41))
        self.label_2.setObjectName("label_2")

        # 调整日志显示框的位置，使其左起点与“运行日志”标签对齐
        self.textBrowser = QtWidgets.QTextBrowser(self.page)
        self.textBrowser.setGeometry(QtCore.QRect(20, 440, 571, 141))
        font = QtGui.QFont()
        font.setPointSize(12)
        self.textBrowser.setFont(font)
        self.textBrowser.setStyleSheet("QTextBrowser {\n"
"    background-color: rgba(0, 0, 0, 0);\n"
"    border: none; /* 可选：移除边框 */\n"
"}")
        self.textBrowser.setObjectName("textBrowser")

        self.stackedWidget.addWidget(self.page)

#-------------以上为首页区-------------------------------------------------



        setpage(self)#设置区

        usersetpage(self)#用户中心区


        icon6 = QtGui.QIcon()
        icon6.addPixmap(QtGui.QPixmap(f"{os.getcwd()}/Static/ProgramImage/info.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)

        MainWindow.setCentralWidget(self.centralwidget)
        self.statusbar = QtWidgets.QStatusBar(MainWindow)
        self.statusbar.setObjectName("statusbar")
        MainWindow.setStatusBar(self.statusbar)


        self.pushButton_yemian2.clicked.connect(lambda: self.stackedWidget.setCurrentIndex(1))  #微信设置
        self.pushButton_zhuye2.clicked.connect(lambda: self.stackedWidget.setCurrentIndex(0))

        self.pushButton_yemian3.clicked.connect(lambda: self.stackedWidget.setCurrentIndex(2))
        self.pushButton_zhuye3.clicked.connect(lambda: self.stackedWidget.setCurrentIndex(0))

        self.retranslateUi(MainWindow)
        self.stackedWidget.setCurrentIndex(0)
        QtCore.QMetaObject.connectSlotsByName(MainWindow)



    def retranslateUi(self, MainWindow):
        _translate = QtCore.QCoreApplication.translate
        MainWindow.setWindowTitle(_translate("MainWindow", "爱嘉客服-AI智能客服"))

        self.label_2.setText(_translate("MainWindow", "<html><head/><body><p><span style=\" font-size:12pt;\">运行日志</span></p></body></html>"))
        self.label_10.setText(_translate("MainWindow",
                                         "<html><head/><body><p><span style=\" font-size:12pt;\">企微人工客服：</span></p></body></html>"))
        # self.label_11.setText(_translate("MainWindow", "<html><head/><body><p><span style=\" font-size:14pt;\">www.aijiakefu.com</span></p></body></html>"))
        self.label_12.setText(_translate("MainWindow",
                                         "<html><head/><body><p><span style=\" font-size:14pt;\">设置</span></p></body></html>"))
        self.label_13.setText(_translate("MainWindow",
                                         "<html><head/><body><p><span style=\" font-size:11pt;\">v1.0.0</span></p></body></html>"))
        self.pushButton_13.setText(_translate("MainWindow", "保存"))
        # self.label_15.setText(_translate("MainWindow",
                                        #  "<html><head/><body><p><span style=\" font-size:12pt;\">昵称</span></p></body></html>"))
        self.label_23.setText(_translate("MainWindow", "<html><head/><body><p><span style=\" font-size:12pt;\">授权</span></p></body></html>"))
        self.label_24.setText(_translate("MainWindow", "<html><head/><body><p><span style=\" font-size:14pt;\">用户中心</span></p></body></html>"))
        self.pushButton_14.setText(_translate("MainWindow", "保存"))
        self.label_25.setText(_translate("MainWindow", "<html><head/><body><p><span style=\" font-size:14pt;\">www.aijiakefu.com</span></p></body></html>"))
        self.lineEdit_6.setText(_translate("MainWindow", "2026-05-20"))
        self.label_26.setText(_translate("MainWindow", "<html><head/><body><p><span style=\" font-size:12pt;\">到期时间</span></p></body></html>"))
        self.label_27.setText(_translate("MainWindow", "<html><head/><body><p><span style=\" font-size:11pt;\">v1.0.0</span></p></body></html>"))
        self.radioButton_3.setText(_translate("MainWindow", "开启"))
        self.label_28.setText(_translate("MainWindow", "<html><head/><body><p><span style=\" font-size:12pt;\">设备绑定数</span></p></body></html>"))
        self.label_29.setText(_translate("MainWindow", "<html><head/><body><p><span style=\" font-size:12pt;\">用户名</span></p></body></html>"))
        self.pushButton_12.setText(_translate("MainWindow", "申请试用"))
        # self.lineEdit_7.setText(_translate("MainWindow", "Sunny6"))
        self.lineEdit_8.setText(_translate("MainWindow", "1"))# 设备绑定数
        self.lineEdit_9.setText(_translate("MainWindow", "1"))
        self.label_30.setText(_translate("MainWindow", "<html><head/><body><p><span style=\" font-size:12pt;\">剩余可用</span></p></body></html>"))
        self.pushButton_15.setText(_translate("MainWindow", "开通续费"))
        
        self.version_label.setText(_translate("MainWindow", f"版本: {LOCAL_VERSION}"))

    def open_proxy_config_page(self):
        self.proxy_config_page.show()
    def open_multi_instance_control_window(self):
        self.multi_instance_window = MultiInstanceControlWindow()
        self.multi_instance_window.show()
    def show_multi_instance_dialog(self):
        dialog = MultiInstanceDialog(self.state, self.shop_names, self.web_drivers, self.proxy_servers)
        if dialog.exec() == QDialog.Accepted:
            self.state, self.shop_names = dialog.save_state()
            self.web_drivers = dialog.web_drivers
            self.proxy_servers = dialog.proxy_servers
    #京东多开对话框
    def show_JD_multi_instance_dialog(self):
        dialogJD = JDMultiInstanceDialog(self.stateJD, self.shop_namesJD, self.web_driversJD, self.proxy_serversJD)
        if dialogJD.exec() == QDialog.Accepted:
            self.stateJD, self.shop_namesJD = dialogJD.save_state()
            self.web_driversJD = dialogJD.web_drivers
            self.proxy_serversJD = dialogJD.proxy_servers
    def show_PDD_multi_instance_dialog(self):
        dialogPDD = PDDMultiInstanceDialog(self.statePDD, self.shop_namesPDD, self.web_driversPDD, self.proxy_serversPDD)
        if dialogPDD.exec() == QDialog.Accepted:
            self.statePDD, self.shop_namesPDD = dialogPDD.save_state()
            self.web_driversPDD = dialogPDD.web_drivers
            self.proxy_serversPDD = dialogPDD.proxy_servers


    def show_KS_multi_instance_dialog(self):
        dialogKS = KSMultiInstanceDialog(self.stateKS, self.shop_namesKS, self.web_driversKS, self.proxy_serversKS)
        if dialogKS.exec() == QDialog.Accepted:
            self.stateKS, self.shop_namesKS = dialogKS.save_state()
            self.web_driversKS = dialogKS.web_drivers
            self.proxy_serversKS = dialogKS.proxy_servers
    def show_douyin_multi_instance_dialog(self):
        dialogdouyin = douyinMultiInstanceDialog(self.statedouyin, self.shop_namesdouyin, self.web_driversdouyin, self.proxy_serversdouyin)
        if dialogdouyin.exec() == QDialog.Accepted:
            self.statedouyin, self.shop_namesdouyin = dialogdouyin.save_state()
            self.web_driversdouyin = dialogdouyin.web_drivers
            self.proxy_serversdouyin = dialogdouyin.proxy_servers


    def show_zhima_multi_instance_dialog(self):
        dialogzhima = zhimaMultiInstanceDialog(self.statezhima, self.shop_nameszhima, self.web_driverszhima, self.proxy_serverszhima)
        if dialogzhima.exec() == QDialog.Accepted:
            self.statezhima, self.shop_nameszhima = dialogzhima.save_state()
            self.web_driverszhima = dialogzhima.web_drivers
            self.proxy_serverszhima = dialogzhima.proxy_servers

if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    MainWindow = QtWidgets.QMainWindow()
    ui = Ui_MainWindow()
    ui.setupUi(MainWindow)
    MainWindow.show()
    sys.exit(app.exec_())