#Tool\login.py
import sys
import os
from PyQt5.QtWidgets import <PERSON>A<PERSON><PERSON>, QMainWindow, QLabel, QVBoxLayout, QWidget, QLineEdit, QPushButton, QMessageBox
from PyQt5.QtGui import QIcon, QPixmap
from PyQt5.QtCore import pyqtSignal, QUrl, Qt, QThread
from PyQt5.QtWebEngineWidgets import QWebEngineView
from Tool.auth_manager import AuthManager
from Tool.utils import add_baidu_analytics  # 导入百度统计
from Tool.Register_window import RegisterWindow  # 导入注册页
from Tool.wechat_api import get_access_token, create_qr_code, get_qr_code_url
class LoginThread(QThread):
    def __init__(self, auth_manager, username, password):
        super().__init__()

        self.auth_manager = auth_manager
        self.username = username
        self.password = password

    def run(self):
        self.auth_manager.start_login(self.username, self.password)

class LoginWindow(QMainWindow):
    login_successful = pyqtSignal(str, str, str)  # 更新信号参数为3个

    def __init__(self, auth_manager, show_qr_code=True):
        super(LoginWindow, self).__init__()
        self.auth_manager = auth_manager
        self.auth_manager.login_successful.connect(self.on_login_successful)
        self.auth_manager.login_failed.connect(self.on_login_failed)
        self.show_qr_code = show_qr_code
        self.logo_click_count = 0  # 初始化点击计数器
        self.initUI()

    def initUI(self):
        self.setWindowTitle('爱嘉客服-登录')
        if self.show_qr_code:
            self.setGeometry(100, 100, 300, 600)
        else:
            self.setGeometry(100, 100, 300, 400)  # 高度减

        self.setWindowIcon(QIcon("./Static/ProgramImage/logo.ico"))

        logo_path = os.path.abspath(r'.\Static\ProgramImage\logo.png')

        self.logo_label = QLabel()
        self.logo_label.mousePressEvent = self.logo_clicked  # 绑定点击事件

        if os.path.exists(logo_path):
            pixmap = QPixmap(logo_path)
            if not pixmap.isNull():
                scaled_pixmap = pixmap.scaled(200, 100, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                self.logo_label.setPixmap(scaled_pixmap)
                self.logo_label.setAlignment(Qt.AlignCenter)
                self.logo_label.setFixedSize(200, 100)
            else:
                self.logo_label.setText(f"Failed to load logo image at path: {logo_path}")
                self.logo_label.setAlignment(Qt.AlignCenter)
        else:
            self.logo_label.setText(f"Logo image not found at path: {logo_path}")
            self.logo_label.setAlignment(Qt.AlignCenter)

        if self.show_qr_code:
            access_token = get_access_token()
            qr_code_ticket = create_qr_code(access_token)
            qr_code_url = get_qr_code_url(qr_code_ticket)

            self.web_view = QWebEngineView(self)
            self.web_view.setUrl(QUrl(qr_code_url))
            self.web_view.setFixedSize(200, 200)

            qr_code_text = QLabel('扫码关注“爱嘉客服”快速登录', self)
            qr_code_text.setAlignment(Qt.AlignCenter)
            qr_code_text.setFixedHeight(30)

        self.username_input = QLineEdit(self)
        self.username_input.setFixedSize(200, 30)
        self.username_input.setText('')  # 设置默认用户名  wx-oR1296Lx0jDJHr6nlalAlOqwtCQU
        self.password_input = QLineEdit(self)
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setFixedSize(200, 30)
        self.password_input.setText('')  # 设置默认密码 uZafOdO$KUczraCm$ZP@ely!
        self.login_button = QPushButton('登录', self)
        self.login_button.clicked.connect(self.handle_login)
        self.login_button.setFixedSize(200, 40)

        self.register_button = QPushButton('注册', self)
        self.register_button.clicked.connect(self.handle_register)
        self.register_button.setFixedSize(200, 40)

        self.test_login_button = QPushButton('测试登录', self)
        self.test_login_button.clicked.connect(self.handle_test_login)
        self.test_login_button.setFixedSize(200, 40)
        self.test_login_button.hide()  # 初始隐藏

        self.test_register_button = QPushButton('测试注册', self)
        self.test_register_button.clicked.connect(self.handle_test_register)
        self.test_register_button.setFixedSize(200, 40)
        self.test_register_button.hide()  # 初始隐藏

        layout = QVBoxLayout()
        layout.addWidget(self.logo_label)
        if self.show_qr_code:
            layout.addWidget(self.web_view)
            layout.addWidget(qr_code_text)
        layout.addWidget(QLabel('用户名:', self))
        layout.addWidget(self.username_input)
        layout.addWidget(QLabel('密码:', self))
        layout.addWidget(self.password_input)
        layout.addWidget(self.login_button)
        layout.addWidget(self.register_button)
        layout.addWidget(self.test_login_button)
        layout.addWidget(self.test_register_button)
        layout.setAlignment(Qt.AlignCenter)

        container = QWidget()
        container.setLayout(layout)
        self.setCentralWidget(container)

        # 加载百度统计
        hidden_browser = QWebEngineView(self)  # 创建一个新的 QWebEngineView 实例
        hidden_browser.setFixedSize(1, 1)  # 将其大小设置为1x1像素
        hidden_browser.setVisible(False)  # 隐藏浏览器窗口
        add_baidu_analytics(hidden_browser)

    def handle_login(self):
        username = self.username_input.text()
        password = self.password_input.text()
        self.login_thread = LoginThread(self.auth_manager, username, password)
        self.login_thread.start()

    def handle_register(self):
        # Determine test mode enabled status
        login_test_enabled = False  # 设置为你的测试开启状态
        register_test_enabled = False  # 设置为你的测试开启状态

        print("Opening Register Window with test enabled:", login_test_enabled, register_test_enabled)
        self.close()
        self.register_window = RegisterWindow(self.auth_manager, login_test_enabled, register_test_enabled)
        self.register_window.show()

    def on_login_successful(self, username, avatar_url, role):
        print(f"Signal received in on_login_successful: username={username}, avatar_url={avatar_url}, role={role}")
        self.login_successful.emit(username, role, avatar_url)  # 发出三个参数
        self.close()

    def on_login_failed(self, error_message):
        print(f"Signal received in on_login_failed: {error_message}")
        QMessageBox.critical(self, '登录失败', f'登录失败: {error_message}')

    def logo_clicked(self, event):
        self.logo_click_count += 1
        if self.logo_click_count >= 6:
            self.enable_engineering_mode()
            self.logo_click_count = 0  # 重置计数器

    def enable_engineering_mode(self):
        print("Engineering mode enabled")
        self.username_input.setText('wx-oR1296Lx0jDJHr6nlalAlOqwtCQU')  # 设置默认用户名
        self.password_input.setText('uZafOdO$KUczraCm$ZP@ely!')  # 设置默认密码
        self.test_login_button.show()  # 显示测试登录按钮
        self.test_register_button.show()  # 显示测试注册按钮

    def handle_test_login(self):
        # 调用注册页的 quick_login_test 方法
        self.register_window = RegisterWindow(self.auth_manager, login_test_enabled=True, register_test_enabled=False)
        self.register_window.quick_login_test()

    def handle_test_register(self):
        # 调用注册页的 quick_register_test 方法
        self.register_window = RegisterWindow(self.auth_manager, login_test_enabled=False, register_test_enabled=True)
        self.register_window.quick_register_test()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    auth_manager = AuthManager()
    login_window = LoginWindow(auth_manager, show_qr_code=False)  # 设置是否显示二维码
    login_window.show()
    sys.exit(app.exec_())

