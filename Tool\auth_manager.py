import aiohttp
import asyncio
import logging
from PyQt5.QtCore import QObject, pyqtSignal

# WordPress站点URL和管理员凭据
site_url = 'https://www.aizhinengkefu.com'
admin_username = 'Sunny'
application_password = '4h2aJnAq19CchXA5Vem0WTFo'

# 控制弹窗功能的开关
SHOW_POPUP = True

# 设置日志记录
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

class AuthManager(QObject):
    login_successful = pyqtSignal(str, str, str)  # 发送 username, avatar_url, role
    login_failed = pyqtSignal(str)

    def __init__(self, parent=None):
        super(AuthManager, self).__init__(parent)
        self.base_url = f"{site_url}/wp-json/jwt-auth/v1"
        self.token = None
        self.username = None
        self.avatar_url = None
        self.role = None
        self.event_loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.event_loop)

    async def login(self, username, password):
        logger.debug(f"Attempting login with username: {username}")
        url = f"{self.base_url}/token"
        payload = {
            'username': username,
            'password': password
        }
        async with aiohttp.ClientSession() as session:
            try:
                async with session.post(url, json=payload) as response:
                    response_data = await response.json()
                    logger.debug(f"Login response status code: {response.status}")
                    logger.debug(f"Login response content: {response_data}")
                    if response.status == 200:
                        self.token = response_data.get('token')
                        logger.debug(f"Login successful, token received: {self.token}")
                        await self.get_user_info(session)
                    else:
                        error_message = response_data.get('message', 'Unknown error occurred')
                        if SHOW_POPUP:
                            self.login_failed.emit(error_message)
                        logger.error(f"Login failed: {error_message}")
            except aiohttp.ClientError as e:
                if SHOW_POPUP:
                    self.login_failed.emit(str(e))
                logger.error(f"Login request failed: {str(e)}")

    async def get_user_info(self, session):
        if self.token:
            headers = {
                'Authorization': f'Bearer {self.token}'
            }
            url = f"{site_url}/wp-json/wp/v2/users/me"
            try:
                async with session.get(url, headers=headers) as response:
                    response_data = await response.json()
                    logger.debug(f"User info response status code: {response.status}")
                    logger.debug(f"User info response content: {response_data}")
                    if response.status == 200:
                        user_data = response_data
                        self.username = user_data.get('name')
                        self.avatar_url = user_data.get('avatar_urls', {}).get('96', 'https://picx.zhimg.com/v2-dd8dbe9a2ed6e1ef4b388f57c390a3a0_r.jpg')  # 获取头像URL，默认头像
                        self.role = user_data.get('roles', ['subscriber'])[0]  # 假设角色字段为 'roles'
                        logger.debug(f"User info retrieved: {self.username}, {self.avatar_url}, {self.role}")
                        self.login_successful.emit(self.username, self.avatar_url, self.role)
                    else:
                        error_message = response_data.get('message', 'Failed to fetch user data')
                        if SHOW_POPUP:
                            self.login_failed.emit(error_message)
                        logger.error(f"Failed to fetch user info: {error_message}")
            except aiohttp.ClientError as e:
                if SHOW_POPUP:
                    self.login_failed.emit(str(e))
                logger.error(f"User info request failed: {str(e)}")

    def start_login(self, username, password):
        self.event_loop.run_until_complete(self.login(username, password))
    
    def get_token(self):
        return self.token

if __name__ == '__main__':
    auth_manager = AuthManager()
    auth_manager.start_login(admin_username, application_password)



# #Tool\auth_manager.py
# import aiohttp
# import asyncio
# import logging
# from PyQt5.QtCore import QObject, pyqtSignal

# # WordPress站点URL
# site_url = 'https://www.aizhinengkefu.com'

# # 管理员用户名和应用密码
# admin_username = 'Sunny'
# application_password = '4h2aJnAq19CchXA5Vem0WTFo'

# # 控制弹窗功能的开关
# SHOW_POPUP = True

# # 设置日志记录
# logging.basicConfig(level=logging.DEBUG)
# logger = logging.getLogger(__name__)

# class AuthManager(QObject):
#     login_successful = pyqtSignal(str, str, str)  # 发送 username, avatar_url, role
#     login_failed = pyqtSignal(str)

#     def __init__(self, parent=None):
#         super(AuthManager, self).__init__(parent)
#         self.base_url = f"{site_url}/wp-json/jwt-auth/v1"
#         self.token = None
#         self.username = None
#         self.avatar_url = None
#         self.role = None

#     async def login(self, username, password):
#         logger.debug(f"Attempting login with username: {username}")
#         url = f"{self.base_url}/token"
#         payload = {
#             'username': username,
#             'password': password
#         }
#         async with aiohttp.ClientSession() as session:
#             try:
#                 async with session.post(url, json=payload) as response:
#                     response_data = await response.json()
#                     logger.debug(f"Login response status code: {response.status}")
#                     logger.debug(f"Login response content: {response_data}")
#                     if response.status == 200:
#                         self.token = response_data.get('token')
#                         logger.debug(f"Login successful, token received: {self.token}")
#                         await self.get_user_info(session)
#                     else:
#                         error_message = response_data.get('message', 'Unknown error occurred')
#                         if SHOW_POPUP:
#                             self.login_failed.emit(error_message)
#                         logger.error(f"Login failed: {error_message}")
#             except aiohttp.ClientError as e:
#                 if SHOW_POPUP:
#                     self.login_failed.emit(str(e))
#                 logger.error(f"Login request failed: {str(e)}")

#     async def get_user_info(self, session):
#         if self.token:
#             headers = {
#                 'Authorization': f'Bearer {self.token}'
#             }
#             url = f"{site_url}/wp-json/wp/v2/users/me"
#             try:
#                 async with session.get(url, headers=headers) as response:
#                     response_data = await response.json()
#                     logger.debug(f"User info response status code: {response.status}")
#                     logger.debug(f"User info response content: {response_data}")
#                     if response.status == 200:
#                         user_data = response_data
#                         self.username = user_data.get('name')
#                         self.avatar_url = user_data.get('avatar_urls', {}).get('96', 'https://picx.zhimg.com/v2-dd8dbe9a2ed6e1ef4b388f57c390a3a0_r.jpg')  # 获取头像URL，默认头像
#                         self.role = user_data.get('roles', ['subscriber'])[0]  # 假设角色字段为 'roles'
#                         logger.debug(f"User info retrieved: {self.username}, {self.avatar_url}, {self.role}")
#                         self.login_successful.emit(self.username, self.avatar_url, self.role)
#                     else:
#                         error_message = response_data.get('message', 'Failed to fetch user data')
#                         if SHOW_POPUP:
#                             self.login_failed.emit(error_message)
#                         logger.error(f"Failed to fetch user info: {error_message}")
#             except aiohttp.ClientError as e:
#                 if SHOW_POPUP:
#                     self.login_failed.emit(str(e))
#                 logger.error(f"User info request failed: {str(e)}")

#     def start_login(self, username, password):
#         loop = asyncio.new_event_loop()
#         asyncio.set_event_loop(loop)
#         loop.run_until_complete(self.login(username, password))
    
#     def get_token(self):
#         return self.token

# if __name__ == '__main__':
#     # For testing purposes only
#     auth_manager = AuthManager()
#     auth_manager.start_login('test_username', 'test_password')




# # import requests
# # import base64
# # import logging
# # from PyQt5.QtCore import QObject, pyqtSignal

# # # WordPress站点URL
# # site_url = 'https://www.aizhinengkefu.com'

# # # 管理员用户名和应用密码
# # admin_username = 'Sunny'
# # application_password = '4h2aJnAq19CchXA5Vem0WTFo'

# # # 控制弹窗功能的开关
# # SHOW_POPUP = True

# # # 设置日志记录
# # logging.basicConfig(level=logging.DEBUG)
# # logger = logging.getLogger(__name__)

# # class AuthManager(QObject):
# #     login_successful = pyqtSignal(str, str, str)  # 发送 username, avatar_url, role
# #     login_failed = pyqtSignal(str)

# #     def __init__(self, parent=None):
# #         super(AuthManager, self).__init__(parent)
# #         self.base_url = f"{site_url}/wp-json/jwt-auth/v1"
# #         self.token = None
# #         self.username = None
# #         self.avatar_url = None
# #         self.role = None

# #     def login(self, username, password):
# #         logger.debug(f"Attempting login with username: {username}")
# #         url = f"{self.base_url}/token"
# #         payload = {
# #             'username': username,
# #             'password': password
# #         }
# #         try:
# #             response = requests.post(url, json=payload)
# #             logger.debug(f"Login response status code: {response.status_code}")
# #             logger.debug(f"Login response content: {response.content.decode()}")
# #             if response.status_code == 200:
# #                 self.token = response.json().get('token')
# #                 logger.debug(f"Login successful, token received: {self.token}")
# #                 self.get_user_info()
# #             else:
# #                 error_message = response.json().get('message', 'Unknown error occurred')
# #                 if SHOW_POPUP:
# #                     self.login_failed.emit(error_message)
# #                 logger.error(f"Login failed: {error_message}")
# #         except requests.exceptions.RequestException as e:
# #             if SHOW_POPUP:
# #                 self.login_failed.emit(str(e))
# #             logger.error(f"Login request failed: {str(e)}")

# #     def get_user_info(self):
# #         if self.token:
# #             headers = {
# #                 'Authorization': f'Bearer {self.token}'
# #             }
# #             url = f"{site_url}/wp-json/wp/v2/users/me"
# #             try:
# #                 response = requests.get(url, headers=headers)
# #                 logger.debug(f"User info response status code: {response.status_code}")
# #                 logger.debug(f"User info response content: {response.content.decode()}")
# #                 if response.status_code == 200:
# #                     user_data = response.json()
# #                     self.username = user_data.get('name')
# #                     self.avatar_url = user_data.get('avatar_urls', {}).get('96', '')  # 获取头像URL
# #                     self.role = user_data.get('roles', ['subscriber'])[0]  # 假设角色字段为 'roles'
# #                     logger.debug(f"User info retrieved: {self.username}, {self.avatar_url}, {self.role}")
# #                     self.login_successful.emit(self.username, self.avatar_url, self.role)
# #                 else:
# #                     error_message = response.json().get('message', 'Failed to fetch user data')
# #                     if SHOW_POPUP:
# #                         self.login_failed.emit(error_message)
# #                     logger.error(f"Failed to fetch user info: {error_message}")
# #             except requests.exceptions.RequestException as e:
# #                 if SHOW_POPUP:
# #                     self.login_failed.emit(str(e))
# #                 logger.error(f"User info request failed: {str(e)}")
