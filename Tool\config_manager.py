import os
import yaml
import json
import logging
from PyQt5.QtWidgets import QMessageBox
from PyQt5.QtGui import QIcon

CONFIG_FILE = 'Tool/config.yaml'
SERVER_CONFIG_FILE = 'Tool/serverConfiguration.yaml'
SERVER_CONFIG_SECTION = 'server'
OCR_CONFIG_FILE = 'Tool/ocr_config.json'
ICON_PATH = './Static/ProgramImage/logo.ico'

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class ConfigManager:
    config_not_found_shown = False  # 类变量，跟踪是否已显示过警告

    def __init__(self, config_file=CONFIG_FILE):
        self.config_file = config_file
        self.api_configserver = {}
        self.api_config = {
            "username": "",
            "人工客服": "",
            "抖店人工客服": "",
            "global": {
                "api_base": "",
                "key": ""
            },
            "individual": {
                "企业微信": {"api_base": "", "key": ""},
                "抖音": {"api_base": "", "key": ""},
                "抖店": {"api_base": "", "key": ""},
            }
        }
        self.load_config()
        self.api_configserver = self.load_server_config()

    def load_config(self):
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as config_file:
                    self.api_config = yaml.safe_load(config_file)
                    logging.info("配置读取完成")
            except Exception as e:
                logging.error(f"加载配置文件失败: {e}")
                self.show_message_box(QMessageBox.Critical, "错误", f"加载配置文件失败: {e}")
        else:
            if not ConfigManager.config_not_found_shown:
                logging.warning("配置文件不存在，将使用默认配置。")
                self.show_message_box(QMessageBox.Warning, "警告", "配置文件不存在，将使用默认配置。")
                ConfigManager.config_not_found_shown = True

    def save_config(self):
        try:
            with open(self.config_file, 'w', encoding='utf-8') as config_file:
                yaml.dump(self.api_config, config_file, default_flow_style=False, allow_unicode=True, sort_keys=False)
            logging.info("个人配置已成功保存")
        except Exception as e:
            logging.error(f"保存配置文件失败: {e}")
            self.show_message_box(QMessageBox.Critical, "错误", f"保存个人配置文件失败: {e}")

    def update_global_config(self, api_base, key):
        # 使用固定的API Base
        self.api_config["global"] = {
            "api_base": "http://api.aijiakefu.com/v1",
            "key": key
        }
        self.save_config()
        self.show_message_box(QMessageBox.Information, "成功", "个人配置已成功保存。")

    def update_individual_config(self, platform, api_base, key):
        if "individual" not in self.api_config:
            self.api_config["individual"] = {}
        # 使用固定的API Base
        self.api_config["individual"][platform] = {
            "api_base": "http://api.aijiakefu.com/v1",
            "key": key
        }
        self.save_config()
        self.show_message_box(QMessageBox.Information, "成功", "个人配置已成功保存。")

    def get_server_config(self):
        self.api_configserver = self.load_server_config()
        return self.api_configserver

    def load_server_config(self, config_file=SERVER_CONFIG_FILE, server_config_section=SERVER_CONFIG_SECTION):
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as file:
                    data = yaml.safe_load(file) or {}
                    return data.get(server_config_section, {})
            except Exception as e:
                logging.error(f"Failed to load config: {e}")
                self.show_message_box(QMessageBox.Critical, "错误", f"加载服务器配置文件失败: {e}")
                return {}
        else:
            if not ConfigManager.config_not_found_shown:
                logging.warning("服务器配置文件不存在，将使用默认配置。")
                self.show_message_box(QMessageBox.Warning, "警告", "服务器配置文件不存在，将使用默认配置。")
                ConfigManager.config_not_found_shown = True
            return {}

    def get_ocr_config(self):
        """获取OCR配置"""
        return self.load_ocr_config()

    def load_ocr_config(self, config_file=OCR_CONFIG_FILE):
        """加载OCR配置文件"""
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as file:
                    data = json.load(file)
                    return data
            except Exception as e:
                logging.error(f"Failed to load OCR config: {e}")
                self.show_message_box(QMessageBox.Critical, "错误", f"加载OCR配置文件失败: {e}")
                return {}
        else:
            # 创建默认OCR配置
            default_config = {
                "baidu_ocr": {
                    "app_id": "您的百度OCR APP_ID",
                    "api_key": "您的百度OCR API_KEY",
                    "secret_key": "您的百度OCR SECRET_KEY"
                },
                "remote_ocr": {
                    "server_url": "http://localhost:5000/ocr",
                    "timeout": 30
                },
                "ocr_mode": "remote"
            }
            try:
                with open(config_file, 'w', encoding='utf-8') as file:
                    json.dump(default_config, file, indent=4, ensure_ascii=False)
                logging.info("已创建默认OCR配置文件")
                return default_config
            except Exception as e:
                logging.error(f"创建默认OCR配置文件失败: {e}")
                return {}

    def save_ocr_config(self, ocr_config):
        """保存OCR配置"""
        try:
            with open(OCR_CONFIG_FILE, 'w', encoding='utf-8') as file:
                json.dump(ocr_config, file, indent=4, ensure_ascii=False)

            logging.info("OCR配置已成功保存")
            return True
        except Exception as e:
            logging.error(f"保存OCR配置失败: {e}")
            self.show_message_box(QMessageBox.Critical, "错误", f"保存OCR配置失败: {e}")
            return False

    def update_ocr_mode(self, mode):
        """更新OCR模式"""
        try:
            ocr_config = self.get_ocr_config()
            ocr_config['ocr_mode'] = mode
            return self.save_ocr_config(ocr_config)
        except Exception as e:
            logging.error(f"更新OCR模式失败: {e}")
            return False

    def update_baidu_ocr_config(self, app_id, api_key, secret_key):
        """更新百度OCR配置"""
        try:
            ocr_config = self.get_ocr_config()
            ocr_config['baidu_ocr'] = {
                'app_id': app_id,
                'api_key': api_key,
                'secret_key': secret_key
            }
            return self.save_ocr_config(ocr_config)
        except Exception as e:
            logging.error(f"更新百度OCR配置失败: {e}")
            return False

    def update_remote_ocr_config(self, server_url, timeout):
        """更新远程OCR配置"""
        try:
            ocr_config = self.get_ocr_config()
            ocr_config['remote_ocr'] = {
                'server_url': server_url,
                'timeout': timeout
            }
            return self.save_ocr_config(ocr_config)
        except Exception as e:
            logging.error(f"更新远程OCR配置失败: {e}")
            return False

    def update_server_config(self, key: str, value):
        if key in self.api_configserver:
            if key.endswith("_PORT"):  # Ensure PORT values are integers
                try:
                    value = int(value)
                except ValueError:
                    logging.error(f"{key} 的值必须是整数。")
                    self.show_message_box(QMessageBox.Critical, "错误", f"{key} 的值必须是整数。")
                    return
            self.api_configserver[key] = value
        else:
            logging.error(f"未找到配置项: {key}")
            self.show_message_box(QMessageBox.Critical, "错误", f"未找到配置项: {key}")

    def show_message_box(self, icon_type, title, message):
        msg_box = QMessageBox()
        msg_box.setIcon(icon_type)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setWindowIcon(QIcon(ICON_PATH))
        msg_box.exec_()
