# 百度OCR配置说明

## 概述

系统已升级为支持多种OCR模式的工厂模式架构，包括：
1. **本地百度OCR** - 直接调用百度OCR API
2. **远程OCR服务** - 通过HTTP API调用远程OCR服务

新的配置方式更加灵活，支持动态切换OCR模式和自定义配置。

## 配置文件

### OCR配置文件位置
- 文件路径: `Tool/ocr_config.json`
- 格式: JSON

### 配置文件结构
```json
{
    "baidu_ocr": {
        "app_id": "您的百度OCR APP_ID",
        "api_key": "您的百度OCR API_KEY",
        "secret_key": "您的百度OCR SECRET_KEY"
    },
    "remote_ocr": {
        "server_url": "http://localhost:5000/ocr",
        "timeout": 30
    },
    "ocr_mode": "remote"
}
```

### 配置参数说明

#### OCR模式选择
- `ocr_mode`: OCR工作模式
  - `"local"`: 使用本地百度OCR API
  - `"remote"`: 使用远程OCR服务

#### 本地百度OCR配置
- `app_id`: 百度OCR应用的APP_ID
- `api_key`: 百度OCR应用的API_KEY
- `secret_key`: 百度OCR应用的SECRET_KEY

#### 远程OCR服务配置
- `server_url`: 远程OCR服务的完整URL地址
- `timeout`: 请求超时时间（秒）

## 使用方式

### 1. 通过界面配置
1. 打开主程序
2. 点击"设置"按钮
3. 在"百度OCR服务配置"区域：
   - 选择OCR模式：本地百度OCR 或 远程OCR服务
   - **本地模式**：填写百度OCR的APP_ID、API_KEY、SECRET_KEY
   - **远程模式**：填写OCR服务地址和超时时间
4. 点击"测试OCR连接"验证配置
5. 点击"保存配置"保存设置

### 2. 手动编辑配置文件
直接编辑 `Tool/ocr_config.json` 文件，修改相应参数。

## OCR模式说明

### 本地百度OCR模式
- **优点**: 直接调用百度API，响应快速，稳定性高
- **缺点**: 需要百度OCR账号和密钥，有API调用限制
- **适用场景**: 有百度OCR账号，对稳定性要求高的场景
- **配置要求**: 需要在百度智能云申请OCR应用获取密钥

### 远程OCR服务模式
- **优点**: 可以使用自建或第三方OCR服务，灵活性高
- **缺点**: 依赖网络连接，需要部署OCR服务
- **适用场景**: 有自建OCR服务或使用第三方OCR API的场景
- **配置要求**: 需要可访问的OCR服务地址

## 服务器类型选择

系统提供两种预设配置：

### 官方测试服务器
- 远程OCR服务地址: `http://************:5000/ocr`
- 适用于测试环境

### 本地服务器
- 远程OCR服务地址: `http://localhost:5000/ocr`
- 适用于本地部署的OCR服务

## OCR服务API规范

### 请求格式
```json
{
    "image": "base64编码的图片数据",
    "format": "base64",
    "type": "accurate"
}
```

### 响应格式
```json
{
    "success": true,
    "texts": ["识别到的文字行1", "识别到的文字行2"],
    "error": "错误信息（如果有）"
}
```

### 健康检查接口
- URL: `{server_url的基础地址}/health`
- 方法: GET
- 用于测试服务可用性

## 代码集成

### 获取OCR引擎
```python
from Tool.ocr_factory import get_ocr_factory, get_ocr_engine

# 获取OCR工厂
ocr_factory = get_ocr_factory()

# 获取当前OCR引擎
ocr_engine = get_ocr_engine()
```

### 识别图片文字
```python
# 从文件路径识别
text = ocr_engine.recognize_text("path/to/image.jpg")

# 从字节数据识别
text = ocr_engine.recognize_text_from_bytes(image_bytes)

# 兼容性方法
text = ocr_engine.image_to_text("path/to/image.jpg")
```

### 测试连接
```python
success, message = ocr_factory.test_engine()
if success:
    print("OCR服务可用")
else:
    print(f"OCR服务不可用: {message}")
```

### 切换OCR模式
```python
# 切换到本地模式
success = ocr_factory.switch_engine('local')

# 切换到远程模式
success = ocr_factory.switch_engine('remote')
```

## 错误处理

系统会自动处理以下错误情况：
- 网络连接超时
- 服务器不可达
- 配置文件格式错误
- 图片文件不存在

所有错误都会记录到日志中，并在界面上显示相应的错误信息。

## 测试脚本

运行 `test_ocr_config.py` 可以测试OCR配置是否正确：

```bash
python test_ocr_config.py
```

## 注意事项

### 本地百度OCR模式
1. **SDK依赖**: 需要安装百度OCR SDK: `pip install baidu-aip`
2. **密钥安全**: 请妥善保管百度OCR的密钥信息
3. **API限制**: 注意百度OCR的调用次数和频率限制
4. **网络要求**: 需要能够访问百度OCR API服务

### 远程OCR服务模式
1. **服务地址格式**: 必须包含完整的URL，包括协议、主机、端口和路径
2. **超时时间**: 建议设置为30-60秒，过短可能导致大图片识别失败
3. **网络环境**: 确保客户端能够访问OCR服务器地址
4. **服务兼容性**: OCR服务必须实现规定的API接口格式

### 通用注意事项
1. **模式选择**: 根据实际需求选择合适的OCR模式
2. **配置备份**: 建议备份OCR配置文件
3. **测试验证**: 配置完成后务必进行连接测试

## 百度OCR申请指南

### 申请步骤
1. 访问 [百度智能云](https://cloud.baidu.com/)
2. 注册并登录账号
3. 进入"产品服务" -> "人工智能" -> "文字识别OCR"
4. 创建应用，获取APP_ID、API_KEY、SECRET_KEY
5. 将密钥信息填入配置文件

### 免费额度
- 百度OCR提供一定的免费调用额度
- 超出免费额度后需要付费使用
- 具体额度请查看百度智能云官网

## 迁移说明

从旧版本升级时：
1. 原来的 `OCR_SERVER_IP` 配置将不再使用
2. 系统会自动创建新的OCR配置文件，默认使用远程模式
3. 如需使用本地百度OCR，请申请百度OCR密钥并配置
4. 企业微信和千牛功能需要OCR服务正常工作
5. 建议先测试OCR连接再正式使用
