# 百度OCR配置说明

## 概述

系统已从原来的固定端口OCR服务改为支持百度OCR API的远程服务配置。新的配置方式更加灵活，支持自定义服务地址和超时时间。

## 配置文件

### OCR配置文件位置
- 文件路径: `Tool/ocr_config.json`
- 格式: JSON

### 配置文件结构
```json
{
    "remote_ocr": {
        "server_url": "http://localhost:5000/ocr",
        "timeout": 30
    }
}
```

### 配置参数说明
- `server_url`: 百度OCR服务的完整URL地址
- `timeout`: 请求超时时间（秒）

## 使用方式

### 1. 通过界面配置
1. 打开主程序
2. 点击"设置"按钮
3. 在"百度OCR服务配置"区域填写：
   - OCR服务地址：如 `http://your-server.com:5000/ocr`
   - 超时时间：如 `30`
4. 点击"测试OCR连接"验证配置
5. 点击"保存配置"保存设置

### 2. 手动编辑配置文件
直接编辑 `Tool/ocr_config.json` 文件，修改相应参数。

## 服务器类型选择

系统提供两种预设配置：

### 官方测试服务器
- OCR服务地址: `http://************:5000/ocr`
- 适用于测试环境

### 本地服务器
- OCR服务地址: `http://localhost:5000/ocr`
- 适用于本地部署的OCR服务

## OCR服务API规范

### 请求格式
```json
{
    "image": "base64编码的图片数据",
    "format": "base64",
    "type": "accurate"
}
```

### 响应格式
```json
{
    "success": true,
    "texts": ["识别到的文字行1", "识别到的文字行2"],
    "error": "错误信息（如果有）"
}
```

### 健康检查接口
- URL: `{server_url的基础地址}/health`
- 方法: GET
- 用于测试服务可用性

## 代码集成

### 获取OCR客户端
```python
from Tool.baidu_ocr_client import get_ocr_client

ocr_client = get_ocr_client()
```

### 识别图片文字
```python
# 从文件路径识别
text = ocr_client.recognize_text("path/to/image.jpg")

# 从字节数据识别
text = ocr_client.recognize_text_from_bytes(image_bytes)

# 兼容性方法
text = ocr_client.image_to_text("path/to/image.jpg")
```

### 测试连接
```python
success, message = ocr_client.test_connection()
if success:
    print("OCR服务可用")
else:
    print(f"OCR服务不可用: {message}")
```

## 错误处理

系统会自动处理以下错误情况：
- 网络连接超时
- 服务器不可达
- 配置文件格式错误
- 图片文件不存在

所有错误都会记录到日志中，并在界面上显示相应的错误信息。

## 测试脚本

运行 `test_ocr_config.py` 可以测试OCR配置是否正确：

```bash
python test_ocr_config.py
```

## 注意事项

1. **服务地址格式**: 必须包含完整的URL，包括协议、主机、端口和路径
2. **超时时间**: 建议设置为30-60秒，过短可能导致大图片识别失败
3. **网络环境**: 确保客户端能够访问OCR服务器地址
4. **服务兼容性**: OCR服务必须实现规定的API接口格式

## 迁移说明

从旧版本升级时：
1. 原来的 `OCR_SERVER_IP` 配置将不再使用
2. 系统会自动创建新的OCR配置文件
3. 需要重新配置OCR服务地址
4. 企业微信和千牛功能需要OCR服务正常工作
