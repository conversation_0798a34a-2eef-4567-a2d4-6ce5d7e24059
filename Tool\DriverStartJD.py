import base64
import json
import socket
import subprocess

import base64
import json
import subprocess
import time

def is_port_in_use(port):
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0

def test_is_port_in_use(port):
    if is_port_in_use(port):
        print(f"端口 {port} 正在被使用。")
        return True
    else:
        print(f"端口 {port} 没有被使用。")
        return False
class DriveJD:
    def start_server(self, server_ip, server_port, parameter, debug_port, user_data_dir):
        json_str = json.dumps(parameter, ensure_ascii=False)
        print("json_str是：", json_str)
        # start
        # chrome.exe - -remote - debugging - port = 8989 - -user - data - dir =./ UserData1
        is_in_use = test_is_port_in_use(debug_port)
        if not is_in_use:
            subprocess.run(["start", "chrome.exe", f"--remote-debugging-port={debug_port}", f"--user-data-dir={user_data_dir}"], shell=True)
        driver_params = {
            "serverIp": server_ip,
            "serverPort": server_port,
            "browserName": "chrome",
            "debugPort": debug_port,
            "userDataDir": user_data_dir,
            "browserPath": None,
            "argument": None,
            "extendParam": json_str
        }
        default_params = json.dumps(driver_params, ensure_ascii=False)
        print("京东default_params是：", default_params)

        # 使用 subprocess 启动 WebDriver.exe，并传递参数
        command = ["WebDriver.exe", default_params]
        process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

        # 获取进程的标准输出和错误输出
        stdout, stderr = process.communicate()

        # 处理输出
        print("Standard Output:", stdout.decode())
        print("Standard Error:", stderr.decode())

# 测试启动服务器
if __name__ == "__main__":
    douyin = DriveJD()
    douyin.start_server("127.0.0.1", 9997, {"key": "value"}, 8990, "./UserData/1")
    # time.sleep(4)
    # douyin.start_server("*************", 9997, {"key": "value"}, 8990, "./UserData/1")
    # time.sleep(4)
    # douyin.start_server("*************", 9997, {"key": "value"}, 8990, "./UserData/1")
    # time.sleep(4)





# class DriveDouyin:
#     def start_server(self, server_ip, server_port, parameter):
#
#         json_str = json.dumps(parameter, ensure_ascii=False)
#         print("编码前的 JSON 字符串:")
#         print(server_ip,":",server_port)
#         print(json_str)
#         encoded_str = base64.b64encode(json_str.encode('utf-8')).decode('utf-8')
#         print("\n编码后的 Base64 字符串:")
#         print(encoded_str)
#         # print("str(parameter)后：",str(parameter))
#         driver_params = {
#             "serverIp": server_ip,
#             "serverPort": server_port,
#             "browserName": "chrome",
#             "debugPort": 8989,
#             "userDataDir": "./UserData",
#             "browserPath": None,
#             "argument": None,
#             "extendParam": encoded_str
#         }
#         default_params = json.dumps(driver_params)
#
#         # 使用 subprocess 启动 WebDriver.exe，并传递参数
#         command = ["WebDriver.exe", default_params]
#         process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
#
#         # 获取进程的标准输出和错误输出
#         stdout, stderr = process.communicate()
#
#         # 处理输出
#         print("Standard Output:", stdout.decode())
#         print("Standard Error:", stderr.decode())
#
# # 测试启动服务器
# if __name__ == "__main__":
#     douyin = DriveDouyin()
#     douyin.start_server("***************", 9998, {"key": "value"})