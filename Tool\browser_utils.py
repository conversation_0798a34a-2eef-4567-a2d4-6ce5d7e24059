# browser_utils.py

import os
import subprocess
import socket,time
from PyQt5.QtWidgets import QMessageBox

def check_and_close_browser():
    """功能是检查指定端口（8989）是否被占用，如果没有被占用，则检查是否有指定的浏览器进程在运行。
    如果有浏览器进程在运行，则提示用户是否要关闭浏览器。如果用户选择关闭，则关闭浏览器进程,启动8989端口浏览器，如果用户未关闭不启动浏览器。
    最后，如果没有检测到浏览器进程启动8989端口浏览器。
    """
    browser_names = ['chrome.exe']
    browser_detected = False
    def is_port_in_use(port):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            return s.connect_ex(('localhost', port)) == 0
        # 抖音启动的浏览器端口
    debug_port = 8989
    # 8989没打开就打开，打开了就不操作跳过
    if not is_port_in_use(debug_port):
        for browser in browser_names:
            try:
                # Check if the browser process is running
                output = os.popen(f'tasklist /FI "IMAGENAME eq {browser}"').read()
                if browser in output:
                    browser_detected = True
                    reply = QMessageBox.question(None, '关闭浏览器', f'检测到浏览器 {browser} 已经打开，是否关闭它？',
                                                 QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes)
                    if reply == QMessageBox.Yes:
                        # Close the browser process
                        os.system(f'taskkill /F /IM {browser}')
                        print(f'{browser} 已关闭')
                    # else:
                        browser_detected = False

                    break
            except Exception as e:
                print(f'检测或关闭 {browser} 进程时发生错误: {e}')
        print(5666)
        if not browser_detected:
            print(debug_port)
            subprocess.run(["start", "chrome.exe", f"--remote-debugging-port={debug_port}"], shell=True)
            time.sleep(1)  # 等待浏览器完全启动
            print('未检测到任何指定的浏览器进程')
            return True
        else:
            return False
    return True


import os
import tkinter as tk
from tkinter import messagebox

def is_chrome_installed():
    # Check common Chrome installation paths on Windows
    paths = [
        os.path.expanduser(r'~\AppData\Local\Google\Chrome\Application\chrome.exe'),
        r'C:\Program Files\Google\Chrome\Application\chrome.exe',
        r'C:\Program Files (x86)\Google\Chrome\Application\chrome.exe'
    ]
    
    for path in paths:
        if os.path.exists(path):
            return True
    
    return False

def show_chrome_not_installed_message():
    root = tk.Tk()
    root.withdraw()  # Hide the root window
    messagebox.showwarning("Google Chrome 未安装", "检测到 Google Chrome 未安装。\n请访问以下地址下载：\nhttps://www.google.cn/chrome/")

if __name__ == "__main__":
    if not is_chrome_installed():
        show_chrome_not_installed_message()
    else:
        print("Google Chrome is installed.")