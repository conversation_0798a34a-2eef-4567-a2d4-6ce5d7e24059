#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR使用示例
演示如何使用新的OCR工厂模式
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Tool.ocr_factory import get_ocr_factory, get_ocr_engine
from Tool.config_manager import ConfigManager

def example_basic_usage():
    """基本使用示例"""
    print("=== OCR基本使用示例 ===")
    
    # 获取OCR引擎
    ocr_engine = get_ocr_engine()
    
    if ocr_engine is None:
        print("❌ OCR引擎未初始化，请检查配置")
        return
    
    print(f"✅ 当前OCR引擎: {type(ocr_engine).__name__}")
    print(f"✅ 引擎状态: {'已初始化' if ocr_engine.is_initialized() else '未初始化'}")
    
    # 示例：识别图片文字（需要有实际的图片文件）
    # image_path = "test_image.jpg"
    # if os.path.exists(image_path):
    #     text = ocr_engine.recognize_text(image_path)
    #     print(f"识别结果: {text}")
    # else:
    #     print("示例图片文件不存在，跳过识别测试")

def example_mode_switch():
    """模式切换示例"""
    print("\n=== OCR模式切换示例 ===")
    
    ocr_factory = get_ocr_factory()
    config_manager = ConfigManager()
    
    # 获取当前配置
    current_config = config_manager.get_ocr_config()
    current_mode = current_config.get('ocr_mode', 'remote')
    print(f"当前OCR模式: {current_mode}")
    
    # 切换模式示例
    new_mode = 'local' if current_mode == 'remote' else 'remote'
    print(f"尝试切换到: {new_mode}")
    
    success = ocr_factory.switch_engine(new_mode)
    if success:
        print(f"✅ 成功切换到 {new_mode} 模式")
        
        # 测试新模式
        success, message = ocr_factory.test_engine()
        if success:
            print(f"✅ 新模式测试成功: {message}")
        else:
            print(f"❌ 新模式测试失败: {message}")
            
        # 切换回原模式
        ocr_factory.switch_engine(current_mode)
        print(f"已切换回原模式: {current_mode}")
    else:
        print(f"❌ 切换到 {new_mode} 模式失败")

def example_config_management():
    """配置管理示例"""
    print("\n=== OCR配置管理示例 ===")
    
    config_manager = ConfigManager()
    
    # 读取当前配置
    ocr_config = config_manager.get_ocr_config()
    print("当前OCR配置:")
    print(f"  模式: {ocr_config.get('ocr_mode', 'unknown')}")
    
    baidu_config = ocr_config.get('baidu_ocr', {})
    print(f"  百度OCR APP_ID: {baidu_config.get('app_id', '未配置')}")
    
    remote_config = ocr_config.get('remote_ocr', {})
    print(f"  远程服务地址: {remote_config.get('server_url', '未配置')}")
    print(f"  超时时间: {remote_config.get('timeout', '未配置')}秒")
    
    # 配置更新示例（仅演示，不实际保存）
    print("\n配置更新示例:")
    print("1. 更新百度OCR配置:")
    print("   config_manager.update_baidu_ocr_config('your_app_id', 'your_api_key', 'your_secret_key')")
    
    print("2. 更新远程OCR配置:")
    print("   config_manager.update_remote_ocr_config('http://your-server:5000/ocr', 30)")
    
    print("3. 切换OCR模式:")
    print("   config_manager.update_ocr_mode('local')  # 或 'remote'")

def example_error_handling():
    """错误处理示例"""
    print("\n=== OCR错误处理示例 ===")
    
    ocr_factory = get_ocr_factory()
    
    # 测试当前引擎
    success, message = ocr_factory.test_engine()
    if success:
        print(f"✅ OCR引擎正常: {message}")
    else:
        print(f"❌ OCR引擎异常: {message}")
        print("可能的解决方案:")
        print("1. 检查网络连接")
        print("2. 验证配置信息是否正确")
        print("3. 确认OCR服务是否可用")
        print("4. 尝试切换OCR模式")

def main():
    """主函数"""
    print("OCR工厂模式使用示例")
    print("=" * 50)
    
    try:
        # 基本使用
        example_basic_usage()
        
        # 模式切换
        example_mode_switch()
        
        # 配置管理
        example_config_management()
        
        # 错误处理
        example_error_handling()
        
    except Exception as e:
        print(f"示例运行出错: {str(e)}")
    
    print("\n示例运行完成！")

if __name__ == "__main__":
    main()
