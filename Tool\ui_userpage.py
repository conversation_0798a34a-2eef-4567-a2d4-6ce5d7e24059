import os
from PyQt5 import QtWidgets, <PERSON>t<PERSON><PERSON>, QtG<PERSON>
def usersetpage(self):#设置区
    # 用户中心

    icon5 = QtGui.QIcon()
    icon5.addPixmap(QtGui.QPixmap(f"{os.getcwd()}/Static/ProgramImage/home.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
    self.page_12 = QtWidgets.QWidget()
    self.page_12.setObjectName("page_12")
    self.label_23 = QtWidgets.QLabel(self.page_12)
    self.label_23.setGeometry(QtCore.QRect(0, 280, 51, 41))
    self.label_23.setObjectName("label_23")
    self.pushButton_zhuye3 = QtWidgets.QPushButton(self.page_12)
    self.pushButton_zhuye3.setGeometry(QtCore.QRect(460, 20, 35, 35))
    self.pushButton_zhuye3.setStyleSheet("QPushButton {\n"
                                         "                background-color: rg<PERSON>(0, 0, 0, 0);\n"
                                         "            }")
    self.pushButton_zhuye3.setText("")
    self.pushButton_zhuye3.setIcon(icon5)
    self.pushButton_zhuye3.setIconSize(QtCore.QSize(35, 35))
    self.pushButton_zhuye3.setObjectName("pushButton_zhuye3")
    self.label_24 = QtWidgets.QLabel(self.page_12)
    self.label_24.setGeometry(QtCore.QRect(220, 50, 101, 41))
    self.label_24.setObjectName("label_24")
    self.pushButton_14 = QtWidgets.QPushButton(self.page_12)
    self.pushButton_14.setGeometry(QtCore.QRect(190, 470, 111, 31))
    self.pushButton_14.setStyleSheet("\n"
                                     "QPushButton {\n"
                                     "    color: rgb(255, 255, 255);\n"
                                     "    background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 lightgreen, stop:1 lightblue);\n"
                                     "    border: none;\n"
                                     "    border-radius: 10px;\n"
                                     "    padding: 1px;\n"
                                     "    font-size: 16px;\n"
                                     "}\n"
                                     "\n"
                                     "QPushButton:hover {\n"
                                     "    padding-bottom: 1px;\n"
                                     "}")
    self.pushButton_14.setObjectName("pushButton_14")
    self.label_25 = QtWidgets.QLabel(self.page_12)
    self.label_25.setGeometry(QtCore.QRect(170, 540, 221, 31))
    self.label_25.setObjectName("label_25")
    self.lineEdit_6 = QtWidgets.QLineEdit(self.page_12)
    self.lineEdit_6.setGeometry(QtCore.QRect(120, 330, 141, 21))
    self.lineEdit_6.setObjectName("lineEdit_6")
    self.label_26 = QtWidgets.QLabel(self.page_12)
    self.label_26.setGeometry(QtCore.QRect(0, 320, 161, 41))
    self.label_26.setObjectName("label_26")
    self.label_27 = QtWidgets.QLabel(self.page_12)
    self.label_27.setGeometry(QtCore.QRect(630, 560, 81, 21))
    self.label_27.setObjectName("label_27")
    self.radioButton_3 = QtWidgets.QRadioButton(self.page_12)
    self.radioButton_3.setGeometry(QtCore.QRect(120, 290, 81, 19))
    self.radioButton_3.setObjectName("radioButton_3")
    self.label_28 = QtWidgets.QLabel(self.page_12)
    self.label_28.setGeometry(QtCore.QRect(0, 350, 111, 41))
    self.label_28.setObjectName("label_28")
    self.label_29 = QtWidgets.QLabel(self.page_12)
    self.label_29.setGeometry(QtCore.QRect(0, 250, 71, 41))
    self.label_29.setObjectName("label_29")
    self.pushButton_12 = QtWidgets.QPushButton(self.page_12)
    self.pushButton_12.setGeometry(QtCore.QRect(210, 150, 101, 41))
    self.pushButton_12.setStyleSheet("border: 1px solid black; border-radius: 6px; background-color: rgba(0, 0, 0, 0);")
    self.pushButton_12.setObjectName("pushButton_12")
    self.lineEdit_7 = QtWidgets.QLineEdit(self.page_12)# 用户名
    self.lineEdit_7.setGeometry(QtCore.QRect(120, 260, 141, 21))
    self.lineEdit_7.setObjectName("lineEdit_7")
    self.lineEdit_8 = QtWidgets.QLineEdit(self.page_12)
    self.lineEdit_8.setGeometry(QtCore.QRect(120, 360, 141, 21))
    self.lineEdit_8.setObjectName("lineEdit_8")
    self.lineEdit_9 = QtWidgets.QLineEdit(self.page_12)
    self.lineEdit_9.setGeometry(QtCore.QRect(430, 360, 141, 21))
    self.lineEdit_9.setObjectName("lineEdit_9")
    self.label_30 = QtWidgets.QLabel(self.page_12)
    self.label_30.setGeometry(QtCore.QRect(310, 350, 111, 41))
    self.label_30.setObjectName("label_30")
    self.pushButton_15 = QtWidgets.QPushButton(self.page_12)
    self.pushButton_15.setGeometry(QtCore.QRect(240, 290, 71, 31))
    self.pushButton_15.setStyleSheet("border: 1px solid black; border-radius: 6px; background-color: rgba(0, 0, 0, 0);")
    self.pushButton_15.setObjectName("pushButton_15")
    self.stackedWidget.addWidget(self.page_12)
    self.label = QtWidgets.QLabel(self.centralwidget)
    self.label.setGeometry(QtCore.QRect(10, 10, int(100 * 0.6), int(125 * 0.6)))
    self.label.setText("")
    self.label.setPixmap(QtGui.QPixmap(f"{os.getcwd()}/Static/ProgramImage/logo.png"))
    self.label.setScaledContents(True)
    self.label.setObjectName("label")

