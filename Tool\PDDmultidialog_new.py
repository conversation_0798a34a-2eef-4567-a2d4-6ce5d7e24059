import sys
import time
import json
import yaml
from typing import Dict, Any
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QPushButton, QDialog, QCheckBox, QLabel, QSizePolicy, QHBoxLayout, QLineEdit, QTableWidget, QTableWidgetItem
from PyQt5.QtCore import QThread, pyqtSignal, Qt
from PyQt5.QtGui import QIcon
from Tool.JDmultiwebdriver import WebDriver #pdd适用
from Tool.ClietServer import ServerAgency


class PDDMultiInstanceDialog(QDialog):
    proxy_configs = {
        1: ('127.0.3.1', 8671),
        2: ('127.0.3.2', 8672),
        3: ('127.0.3.3', 8673),
        4: ('127.0.3.4', 8674),
        5: ('127.0.3.5', 8675),
        6: ('127.0.3.6', 8676),
        7: ('127.0.3.7', 8677),
        8: ('127.0.3.8', 8678)
    }

    def __init__(self, states, shop_names, web_drivers, proxy_servers, parent=None):
        super().__init__(parent)
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowContextHelpButtonHint)
        self.setWindowTitle('拼多多多开控制面板')
        self.setWindowIcon(QIcon("./Static/ProgramImage/logo.ico"))
        self.setFixedSize(600, 400)

        self.main_layout = QVBoxLayout(self)
        self.checkboxes = []
        self.shop_name_edits = []
        self.web_drivers = web_drivers
        self.proxy_servers = proxy_servers
        self.base_debug_port = 8591
        # 存储待启动的实例索引
        self.pending_instances = []
        merged_config = self.merge_configs('Tool/serverConfiguration.yaml', 'Tool/config.yaml', 'Tool/config.json')
        print("初始中获取的配置是：", merged_config)
        checkbox_layout = QVBoxLayout()
        for i in range(1, 9):
            checkbox_hbox = QHBoxLayout()
            checkbox = QCheckBox(f'拼多多多开{i}', self)
            # 修改这里，不再连接toggle_instance，而是连接到update_pending_instances
            checkbox.stateChanged.connect(self.update_pending_instances)
            self.checkboxes.append(checkbox)
            checkbox_hbox.addWidget(checkbox)

            shop_name_edit = QLineEdit(self)
            shop_name_edit.setFixedSize(180, 26)
            shop_name_edit.setReadOnly(True)
            self.shop_name_edits.append(shop_name_edit)
            checkbox_hbox.addWidget(shop_name_edit)

            checkbox_layout.addLayout(checkbox_hbox)

        hbox = QHBoxLayout()
        checkbox_container = QWidget()
        checkbox_container.setLayout(checkbox_layout)
        hbox.addWidget(checkbox_container)

        explanation_label = QLabel('请在设置页面编辑好配置，再继续拼多多多开。\n\n'
            '请勾选需要启动的拼多多多开实例，然后点击启动多开按钮启动所选实例。\n\n'
            '每个实例将启动一个独立的拼多多窗口，允许你同时管理多个拼多多账号。\n\n',
            self)
        explanation_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        explanation_label.setWordWrap(True)
        explanation_label.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        explanation_label.setStyleSheet(
            "font-size: 12pt; color: #333; background-color: #f0f0f0; padding: 10px; border-radius: 5px;")
        hbox.addWidget(explanation_label)

        hbox.setStretch(0, 1)
        hbox.setStretch(1, 2)
        self.main_layout.addLayout(hbox)

        self.complete_button = QPushButton('启动多开', self)
        self.complete_button.setFixedSize(100, 35)  # 调整尺寸为100x35
        self.complete_button.setStyleSheet("""
            QPushButton {
                background-color: qlineargradient(
                    spread:pad, x1:0, y1:0, x2:1, y2:1, 
                    stop:0 rgba(85, 170, 255, 255), 
                    stop:1 rgba(0, 85, 255, 255)
                );  /* 使用渐变色背景 */
                color: white;  /* 白色字体 */
                font-size: 11pt;  /* 字体稍微小一点 */
                font-weight: bold;  /* 字体加粗 */
                border-radius: 10px;  /* 圆角边框 */
                border: 2px solid #0055ff;  /* 蓝色边框 */
                padding: 5px;  /* 增加内边距 */
            }
            QPushButton:hover {
                background-color: qlineargradient(
                    spread:pad, x1:0, y1:0, x2:1, y2:1, 
                    stop:0 rgba(0, 85, 255, 255), 
                    stop:1 rgba(85, 170, 255, 255)
                );  /* 鼠标悬停时反转渐变色 */
            }
            QPushButton:pressed {
                background-color: #0044cc;  /* 按下时变为深蓝色 */
            }
        """)
        # 修改按钮点击事件，连接到启动实例的方法
        self.complete_button.clicked.connect(self.start_pending_instances)
        self.main_layout.addWidget(self.complete_button, alignment=Qt.AlignCenter)

        self.load_state(states, shop_names)
        for i in range(1, 9):
            shop_key = f'拼多多多开{i}'
            # 检查是否存在该shop_key，避免KeyError
            if shop_key in merged_config:
                shop_name = merged_config[shop_key].get('Shop Name', '')
                print(f"正在获取 {shop_key}，店铺名称为：{shop_name}")
                self.shop_name_edits[i - 1].setText(f"店铺名称：{shop_name}")
            else:
                print(f"{shop_key} 不存在于配置中")

    def merge_configs(self, server_config_path: str, config_yaml_path: str, config_json_path: str,
                      duokaidict: Dict[str, Any] = {}) -> Dict[str, Any]:
        def load_yaml(file_path: str) -> Dict[str, Any]:
            try:
                with open(file_path, 'r', encoding='utf-8') as file:
                    content = yaml.safe_load(file)
                    if isinstance(content, list):
                        content = {str(index): value for index, value in enumerate(content)}
                    elif not isinstance(content, dict):
                        raise TypeError(f"{file_path} content is neither a dictionary nor a list")
                    return content
            except FileNotFoundError:
                print(f"File {file_path} not found.")
                return {}
            except yaml.YAMLError as e:
                print(f"Error loading YAML file {file_path}: {e}")
                return {}

        def load_json(file_path: str) -> Dict[str, Any]:
            config_dict = {}
            try:
                with open(file_path, 'r', encoding='utf-8') as file:
                    configs = json.load(file)
                    if not configs:
                        print("Configuration file is empty.")
                        return config_dict

                    for config in configs:
                        config_name = config.get('name', 'N/A')
                        config_dict[config_name] = {
                            "Shop Name": config.get('shop_name', 'N/A'),
                            "API Base": config.get('api_base', 'N/A'),
                            "Key": config.get('key', 'N/A'),
                            "Customer Service Name": config.get('customer_service_name', 'N/A')
                        }
                    return config_dict
            except FileNotFoundError:
                print(f"File {file_path} not found.")
                return config_dict
            except json.JSONDecodeError as e:
                print(f"Error parsing JSON file {file_path}: {e}")
                return config_dict

        server_config = load_yaml(server_config_path)
        config_yaml = load_yaml(config_yaml_path)
        config_json = load_json(config_json_path)

        merged_config = {**server_config, **config_yaml, **config_json, **duokaidict}

        return merged_config

    def update_pending_instances(self, state):
        checkbox = self.sender()
        instance_index = self.checkboxes.index(checkbox) + 1
        if state == Qt.Checked:
            if instance_index not in self.pending_instances:
                self.pending_instances.append(instance_index)
        else:
            if instance_index in self.pending_instances:
                self.pending_instances.remove(instance_index)

    def start_pending_instances(self):
        for instance_index in self.pending_instances:
            self.start_instance(instance_index)
        self.accept()  # 启动完成后关闭对话框

    def start_instance(self, instance_index):
        duokaidict = {"duokaikey": f'拼多多多开{instance_index}'}
        merged_config = self.merge_configs('Tool/serverConfiguration.yaml', 'Tool/config.yaml', 'Tool/config.json',
                                           duokaidict)
        print("获取的配置是：", merged_config)

        webdriver_ip = merged_config['server']['WebDriverServer_IP']
        print("获取到的拼多多服务器ip是：", webdriver_ip)

        target_host_PDD = webdriver_ip
        target_port_PDD = 9994
        proxy_host_PDD, proxy_port_PDD = self.proxy_configs[instance_index]
        PDD_debug_port = self.base_debug_port + instance_index - 1
        user_data_dir = f"./UserData/PDD/{instance_index}"

        parameter = merged_config

        if instance_index not in self.web_drivers:
            # 创建并启动代理服务器
            proxy_server = ServerAgency(target_host_PDD, target_port_PDD, proxy_host_PDD,
                                        proxy_port_PDD)
            self.proxy_servers[instance_index] = proxy_server

            # 创建并启动WebDriver
            web_driver = WebDriver(proxy_host_PDD, proxy_port_PDD, parameter, PDD_debug_port, user_data_dir)
            self.web_drivers[instance_index] = web_driver

            # 启动WebDriver和代理服务器
            try:
                web_driver.start()
                self.proxy_servers[instance_index].ClietServerMain()
            except Exception as e:
                print(f"启动实例 {instance_index} 时出错：{e}")
        else:

            # 重新创建并启动WebDriver
            web_driver = WebDriver(proxy_host_PDD, proxy_port_PDD, parameter, PDD_debug_port, user_data_dir)
            self.web_drivers[instance_index] = web_driver

            try:
                web_driver.start()
                self.proxy_servers[instance_index].ClietServerMain()
            except Exception as e:
                print(f"启动实例 {instance_index} 时出错：{e}")

    def stop_instance(self, instance_index):
        try:
            self.web_drivers[instance_index].stop()
            self.proxy_servers[instance_index].ServerClose()
        except Exception as e:
            self.update_log(f"停止实例 {instance_index} 时发生异常: {e}")

    def update_log(self, message):
        print(message)

    def save_state(self):
        states = [checkbox.isChecked() for checkbox in self.checkboxes]
        shop_names = [edit.text() for edit in self.shop_name_edits]
        return states, shop_names

    def load_state(self, states, shop_names):
        for checkbox, checked in zip(self.checkboxes, states):
            checkbox.setChecked(checked)
        for edit, shop_name in zip(self.shop_name_edits, shop_names):
            edit.setText(shop_name)

    def complete(self):
        self.accept()

    def setup_ui(self):
        # 创建表格
        self.table = QTableWidget(self)
        self.table.setColumnCount(4)
        self.table.setHorizontalHeaderLabels(["店铺名称", "API Base", "Key", "状态"])
        # 隐藏API Base列
        self.table.setColumnHidden(1, True)
        
        # 设置表格内容
        for i in range(8):
            self.table.insertRow(i)
            # 店铺名称
            name_item = QTableWidgetItem(self.shop_names[i])
            self.table.setItem(i, 0, name_item)
            
            # API Base (隐藏并写死)
            api_base_item = QTableWidgetItem("http://api.aijiakefu.com/v1")
            self.table.setItem(i, 1, api_base_item)
            
            # Key
            key_item = QTableWidgetItem("")
            self.table.setItem(i, 2, key_item)
            
            # 状态
            status_item = QTableWidgetItem("未启动")
            self.table.setItem(i, 3, status_item) 