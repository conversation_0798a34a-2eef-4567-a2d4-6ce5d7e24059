import time
from PyQt5.QtCore import QThread, pyqtSignal
from Tool.DriverStartdoudian import DriveDoudian

class WebDriver(QThread, DriveDoudian):
    log_signal = pyqtSignal(str)

    def __init__(self, ip, port, parameter, debug_port, user_data_dir, parent=None):
        super().__init__(parent)
        self.ip = ip
        self.port = port
        self.parameter = parameter
        self.debug_port = debug_port
        self.user_data_dir = user_data_dir
        self.running = False

    def run(self):
        self.running = True
        try:
            self.log_signal.emit(
                f"[{time.strftime('%H:%M:%S')}] start_server 启动，地址：{self.ip}, DebugPort: {self.debug_port}, UserDataDir: {self.user_data_dir}")
            self.start_server(self.ip, self.port, self.parameter, self.debug_port, self.user_data_dir)
            self.log_signal.emit(f"[{time.strftime('%H:%M:%S')}] start_server 完成")
        except Exception as e:
            self.log_signal.emit(f"运行中异常: {e}")
        finally:
            self.running = False

    def stop(self):
        if self.running:
            try:
                self.terminate()
                self.wait()
                self.log_signal.emit(f"[{time.strftime('%H:%M:%S')}] start_server 已停止")
            except Exception as e:
                self.log_signal.emit(f"停止过程中异常: {e}")
            finally:
                self.running = False
