
import subprocess
import platform
import threading

class Driver:
    """
        DEBUG模式下自动启动驱动调试浏览器
        Automatically start the driver DEBUGging browser in debug mode.
    """
    @classmethod
    def WindowsDriverStart(self, IP, Port, Parameter) -> None:
        try:
            system_info = platform.system()
            if system_info == "Windows":
                DriverName = "WindowsDriver.exe"
                version_info = platform.version()
                major_version = int(version_info.split('.')[0])
                if major_version < 10:
                    DriverName = "WindowsDriver_win7.exe"
                print("Debug Model Start WinDriver ...")
                subprocess.Popen([DriverName, str(IP), str(Port), str(Parameter)])
                print("Start WinDriver Successful，Execute Script")
        except FileNotFoundError as e:
            err_msg = "\nStart local WinDriver.exe fail Exception elimination step：\n1. Check WebDriver.exe Path；\n2. WebDriver.exe Add to system environment variable?"
            self.error(f"{err_msg}: " + str(e))

    # @classmethod
    # def main(self, IP, Port):
    #     # 创建一个新线程来处理客户端请求
    #     client_handler = threading.Thread(target=self.WindowsDriverStart, args=(self, IP, Port))
    #     client_handler.start()
