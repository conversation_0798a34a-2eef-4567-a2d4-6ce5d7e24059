import requests
import base64
import json
import os
import logging
from abc import ABC, abstractmethod
from Tool.config_manager import ConfigManager

class RemoteBaiduOCR:
    """远程百度OCR客户端"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.server_url = None
        self.timeout = 30
        self.config_manager = ConfigManager()
        self._init_remote_config()
    
    def _init_remote_config(self):
        """初始化远程服务配置"""
        try:
            ocr_config = self.config_manager.get_ocr_config()
            
            self.server_url = ocr_config.get('server_url', 'http://localhost:5000/ocr')
            self.timeout = ocr_config.get('timeout', 30)
            self.logger.info(f"远程百度OCR客户端初始化成功，服务器地址: {self.server_url}")
        except Exception as e:
            self.logger.error(f"初始化远程百度OCR客户端配置失败: {str(e)}")
            self.server_url = None
    
    def is_initialized(self):
        """检查客户端是否已初始化"""
        return self.server_url is not None
    
    def test_connection(self):
        """测试OCR服务连接"""
        if not self.is_initialized():
            return False, "OCR客户端未初始化"
        
        try:
            # 发送测试请求
            response = requests.get(
                f"{self.server_url.rstrip('/ocr')}/health",
                timeout=5
            )
            if response.status_code == 200:
                return True, "OCR服务连接正常"
            else:
                return False, f"OCR服务响应异常，状态码: {response.status_code}"
        except requests.exceptions.Timeout:
            return False, "OCR服务连接超时"
        except requests.exceptions.ConnectionError:
            return False, "无法连接到OCR服务"
        except Exception as e:
            return False, f"OCR服务连接测试失败: {str(e)}"
    
    def recognize_text(self, image_path):
        """识别图片中的文字"""
        if not self.is_initialized():
            self.logger.error("远程百度OCR客户端未初始化")
            return ""
        
        if not os.path.exists(image_path):
            self.logger.error(f"图片文件不存在: {image_path}")
            return ""
        
        try:
            # 读取图片并转为base64
            with open(image_path, 'rb') as fp:
                image_bytes = fp.read()
                image_base64 = base64.b64encode(image_bytes).decode('utf-8')
            
            # 发送请求到OCR服务器
            response = requests.post(
                self.server_url,
                json={
                    'image': image_base64,
                    'format': 'base64',
                    'type': 'accurate'  # 使用高精度识别
                },
                timeout=self.timeout
            )
            
            # 处理响应
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    texts = result.get('texts', [])
                    recognized_text = "\n".join(texts)
                    self.logger.info(f"OCR识别成功，识别到 {len(texts)} 行文字")
                    return recognized_text
                else:
                    error_msg = result.get('error', '未知错误')
                    self.logger.warning(f"OCR识别失败: {error_msg}")
                    return ""
            else:
                self.logger.error(f"OCR服务请求失败，状态码: {response.status_code}")
                return ""
        
        except requests.exceptions.Timeout:
            self.logger.error("OCR识别请求超时")
            return ""
        except requests.exceptions.ConnectionError:
            self.logger.error("无法连接到OCR服务")
            return ""
        except Exception as e:
            self.logger.error(f"识别文字失败: {str(e)}")
            return ""
    
    def recognize_text_from_bytes(self, image_bytes):
        """从图片字节数据识别文字"""
        if not self.is_initialized():
            self.logger.error("远程百度OCR客户端未初始化")
            return ""
        
        try:
            # 转为base64
            image_base64 = base64.b64encode(image_bytes).decode('utf-8')
            
            # 发送请求到OCR服务器
            response = requests.post(
                self.server_url,
                json={
                    'image': image_base64,
                    'format': 'base64',
                    'type': 'accurate'  # 使用高精度识别
                },
                timeout=self.timeout
            )
            
            # 处理响应
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    texts = result.get('texts', [])
                    recognized_text = "\n".join(texts)
                    self.logger.info(f"OCR识别成功，识别到 {len(texts)} 行文字")
                    return recognized_text
                else:
                    error_msg = result.get('error', '未知错误')
                    self.logger.warning(f"OCR识别失败: {error_msg}")
                    return ""
            else:
                self.logger.error(f"OCR服务请求失败，状态码: {response.status_code}")
                return ""
        
        except requests.exceptions.Timeout:
            self.logger.error("OCR识别请求超时")
            return ""
        except requests.exceptions.ConnectionError:
            self.logger.error("无法连接到OCR服务")
            return ""
        except Exception as e:
            self.logger.error(f"识别文字失败: {str(e)}")
            return ""
    
    def image_to_text(self, image_path):
        """兼容性方法，调用 recognize_text"""
        return self.recognize_text(image_path)

    def update_config(self, server_url, timeout=30):
        """更新OCR配置"""
        try:
            ocr_config = {
                'server_url': server_url,
                'timeout': timeout
            }
            
            if self.config_manager.save_ocr_config(ocr_config):
                self.server_url = server_url
                self.timeout = timeout
                self.logger.info("OCR配置更新成功")
                return True
            else:
                return False
        except Exception as e:
            self.logger.error(f"更新OCR配置失败: {str(e)}")
            return False

# 全局OCR客户端实例
ocr_client = RemoteBaiduOCR()

def get_ocr_client():
    """获取OCR客户端实例"""
    return ocr_client
